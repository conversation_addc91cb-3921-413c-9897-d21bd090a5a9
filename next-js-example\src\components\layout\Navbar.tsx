'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, X, Flag, Users, Trophy, FileText, LogOut } from 'lucide-react';
import { supabase } from '@/lib/supabase/client';

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  
  const isAdmin = pathname.startsWith('/admin');
  
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    window.location.href = '/login';
  };

  const adminMenuItems = [
    { icon: Flag, label: 'Rallies', path: '/admin/rallies' },
    { icon: Users, label: 'Persons', path: '/admin/persons' },
    { icon: Trophy, label: 'Championships', path: '/admin/championships' },
    { icon: FileText, label: 'News', path: '/admin/news' },
  ];

  const publicMenuItems = [
    { label: 'Home', path: '/' },
    { label: 'Rallies', path: '/rallies' },
    { label: 'Drivers', path: '/drivers' },
    { label: 'Championships', path: '/championships' },
  ];

  const menuItems = isAdmin ? adminMenuItems : publicMenuItems;

  return (
    <nav className="bg-white dark:bg-gray-800 shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0 flex items-center">
              <span className="text-xl font-bold text-red-600">Stagetime</span>
            </Link>
            <div className="hidden md:ml-6 md:flex md:space-x-4">
              {menuItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname === item.path
                      ? 'bg-red-600 text-white'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  {isAdmin && item.icon && (
                    <item.icon className="inline-block w-4 h-4 mr-1" />
                  )}
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          
          {isAdmin && (
            <div className="hidden md:flex items-center">
              <button
                onClick={handleLogout}
                className="px-3 py-2 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              >
                <LogOut className="w-4 h-4 mr-1" />
                Logout
              </button>
            </div>
          )}
          
          <div className="flex md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
            >
              {isOpen ? (
                <X className="block h-6 w-6" />
              ) : (
                <Menu className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {menuItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  pathname === item.path
                    ? 'bg-red-600 text-white'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                onClick={toggleMenu}
              >
                {isAdmin && item.icon && (
                  <item.icon className="inline-block w-4 h-4 mr-1" />
                )}
                {item.label}
              </Link>
            ))}
            {isAdmin && (
              <button
                onClick={handleLogout}
                className="w-full text-left block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <LogOut className="inline-block w-4 h-4 mr-1" />
                Logout
              </button>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}
