import pool from './server/config/db.js';
import fs from 'fs';

async function testChampionshipImport() {
  try {
    console.log('=== Testing Championship Import ===');
    
    // Check if we have any entry-championship associations
    const entryChampionships = await pool.query(`
      SELECT 
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        c.name as championship
      FROM entry_championships ec
      JOIN entries e ON ec.entry_id = e.id
      JOIN championships c ON ec.championship_id = c.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN rallies r ON e.rally_id = r.id
      WHERE r.name LIKE '%Stereas Elladas 2025%'
      ORDER BY e.number, c.name
    `);
    
    console.log(`Found ${entryChampionships.rows.length} entry-championship associations:`);
    entryChampionships.rows.forEach(row => {
      console.log(`  Entry #${row.number} (${row.driver}) → ${row.championship}`);
    });
    
    // Check championship standings using new view
    const standings = await pool.query(`
      SELECT 
        championship_name,
        driver,
        total_points,
        position
      FROM championship_standings_new
      ORDER BY championship_name, position
      LIMIT 20
    `);
    
    console.log(`\nChampionship standings (${standings.rows.length} entries):`);
    standings.rows.forEach(row => {
      console.log(`  ${row.championship_name}: ${row.position}. ${row.driver} (${row.total_points} pts)`);
    });
    
    // Check rally results using new view
    const rallyResults = await pool.query(`
      SELECT 
        championship_name,
        driver,
        position,
        championship_points
      FROM championship_overall_classification_new
      WHERE rally_name LIKE '%Stereas Elladas 2025%'
      ORDER BY championship_name, position
      LIMIT 20
    `);
    
    console.log(`\nRally results (${rallyResults.rows.length} entries):`);
    rallyResults.rows.forEach(row => {
      console.log(`  ${row.championship_name}: ${row.position}. ${row.driver} (${row.championship_points} pts)`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing championship import:', error);
    process.exit(1);
  }
}

testChampionshipImport();
