-- Simple Championship System for Rally Stereas Elladas 2025
-- No complex tables, just smart filtering using existing schema

-- Create a simple view for championship results
DROP VIEW IF EXISTS championship_results;

CREATE VIEW championship_results AS
SELECT 
    -- Championship identification
    CASE 
        WHEN ce.championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3' THEN 'Greece'
        WHEN ce.championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17' THEN 'Historic'
        WHEN ce.championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c' THEN 'Historic Gravel Cup'
        WHEN ce.championship_id = '895f159e-f147-439b-b5ab-04972033a7bb' THEN 'Rally3'
        ELSE c.name
    END as championship_name,
    ce.championship_id,
    
    -- Rally information
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    
    -- Entry information
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    
    -- Results
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    
    -- Simple points calculation (25, 18, 15, 12, 10, 8, 6, 4, 2, 1)
    CASE 
        WHEN oc.position = 1 THEN 25
        WHEN oc.position = 2 THEN 18
        WHEN oc.position = 3 THEN 15
        WHEN oc.position = 4 THEN 12
        WHEN oc.position = 5 THEN 10
        WHEN oc.position = 6 THEN 8
        WHEN oc.position = 7 THEN 6
        WHEN oc.position = 8 THEN 4
        WHEN oc.position = 9 THEN 2
        WHEN oc.position = 10 THEN 1
        ELSE 0
    END as points

FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id

WHERE e.status IN ('finished', 'retired', 'dnf')
AND (
    -- Greece Championship: All C-classes
    (ce.championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3' 
     AND (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
          OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%'))
    
    OR
    
    -- Historic Championship: Only numeric classes 2, 3, 4 (exact match)
    (ce.championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17' 
     AND e.class IN ('2', '3', '4'))
    
    OR
    
    -- Historic Gravel Cup: Same as Historic
    (ce.championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c' 
     AND e.class IN ('2', '3', '4'))
    
    OR
    
    -- Rally3 Championship: C2 cars that are Rally3 (identify by car model)
    (ce.championship_id = '895f159e-f147-439b-b5ab-04972033a7bb' 
     AND e.class LIKE '%C2%' 
     AND (e.car LIKE '%Rally3%' OR e.car LIKE '%Clio%' OR e.car LIKE '%208%'))
)

ORDER BY ce.championship_id, r.start_date, oc.position;

-- Create a simple championship standings view
DROP VIEW IF EXISTS championship_standings;

CREATE VIEW championship_standings AS
SELECT 
    championship_id,
    championship_name,
    driver,
    driver_nationality,
    SUM(points) as total_points,
    COUNT(*) as rallies_completed,
    ROW_NUMBER() OVER (PARTITION BY championship_id ORDER BY SUM(points) DESC) as position
FROM championship_results
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, total_points DESC;

-- Test the system
SELECT 'Championship entry distribution:' as info;

SELECT 
    championship_name,
    COUNT(*) as total_entries,
    COUNT(DISTINCT driver) as unique_drivers,
    STRING_AGG(DISTINCT class, ', ' ORDER BY class) as classes_included
FROM championship_results
WHERE rally_name LIKE '%Stereas Elladas%'
GROUP BY championship_id, championship_name
ORDER BY total_entries DESC;

-- Show top 3 in each championship
SELECT 'Top 3 drivers per championship:' as info;

SELECT 
    championship_name,
    position,
    driver,
    total_points,
    rallies_completed
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position;
