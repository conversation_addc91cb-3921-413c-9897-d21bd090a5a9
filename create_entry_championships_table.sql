-- Create table to store entry-championship associations
-- This will track which entries are actually registered for which championships
-- based on the championship data from EWRC JSON

CREATE TABLE IF NOT EXISTS entry_championships (
    id SERIAL PRIMARY KEY,
    entry_id UUID NOT NULL,
    championship_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(entry_id, championship_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_entry_championships_entry ON entry_championships(entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_championships_championship ON entry_championships(championship_id);
CREATE INDEX IF NOT EXISTS idx_entry_championships_combined ON entry_championships(entry_id, championship_id);

-- Add a comment to explain the purpose
COMMENT ON TABLE entry_championships IS 'Stores which entries are registered for which championships based on EWRC data';
COMMENT ON COLUMN entry_championships.entry_id IS 'Reference to the entry';
COMMENT ON COLUMN entry_championships.championship_id IS 'Reference to the championship the entry is registered for';
