-- Cleanup script to remove all rallies and persons
-- IMPORTANT: Run backup_before_cleanup.sql FIRST!

-- Show current counts before cleanup
SELECT 'BEFORE CLEANUP' as section;
SELECT 
  'persons' as table_name, 
  COUNT(*) as current_records
FROM persons
UNION ALL
SELECT 
  'rallies' as table_name, 
  COUNT(*) as current_records
FROM rallies
UNION ALL
SELECT 
  'entries' as table_name, 
  COUNT(*) as current_records
FROM entries
UNION ALL
SELECT 
  'results' as table_name, 
  COUNT(*) as current_records
FROM results
UNION ALL
SELECT 
  'stages' as table_name, 
  COUNT(*) as current_records
FROM stages;

-- Start transaction for safety
BEGIN;

-- Delete in correct order to respect foreign key constraints
DELETE FROM penalties;
DELETE FROM results;
DELETE FROM championship_events;
DELETE FROM entries;
DELETE FROM stages;
DELETE FROM rallies;

-- Remove driver/codriver references
DELETE FROM drivers;
DELETE FROM codrivers;

-- Finally remove persons
DELETE FROM persons;

-- Show counts after cleanup
SELECT 'AFTER CLEANUP' as section;
SELECT 
  'persons' as table_name, 
  COUNT(*) as remaining_records
FROM persons
UNION ALL
SELECT 
  'rallies' as table_name, 
  COUNT(*) as remaining_records
FROM rallies
UNION ALL
SELECT 
  'entries' as table_name, 
  COUNT(*) as remaining_records
FROM entries
UNION ALL
SELECT 
  'results' as table_name, 
  COUNT(*) as remaining_records
FROM results
UNION ALL
SELECT 
  'stages' as table_name, 
  COUNT(*) as remaining_records
FROM stages;

-- COMMIT the transaction (uncomment when you're ready)
-- COMMIT;

-- If something goes wrong, you can ROLLBACK instead:
-- ROLLBACK;

SELECT 'CLEANUP COMPLETED - REMEMBER TO COMMIT!' as status;
