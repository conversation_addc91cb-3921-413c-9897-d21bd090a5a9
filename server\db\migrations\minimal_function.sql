-- Minimal function version to avoid any syntax issues

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS trigger_track_entry_status_change ON entries;
DROP FUNCTION IF EXISTS track_entry_status_change();

-- Create simple function without complex CASE statements
CREATE OR REPLACE FUNCTION track_entry_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only track if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO entry_status_history (
      entry_id, 
      rally_id, 
      old_status, 
      new_status,
      reason
    ) VALUES (
      NEW.id,
      NEW.rally_id,
      OLD.status,
      NEW.status,
      'Status changed from ' || COALESCE(OLD.status, 'null') || ' to ' || NEW.status
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER trigger_track_entry_status_change
  AFTER UPDATE ON entries
  FOR EACH ROW
  EXECUTE FUNCTION track_entry_status_change();
