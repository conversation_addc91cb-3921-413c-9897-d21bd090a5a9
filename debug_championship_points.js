import pool from './server/config/db.js';

async function debugChampionshipPoints() {
  try {
    console.log('=== Debugging Championship Points ===');
    
    // Check overall classification for our test entries
    const overallClassification = await pool.query(`
      SELECT 
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        oc.position,
        oc.total_time,
        oc.time_diff,
        e.status
      FROM entries e
      JOIN persons pd ON e.driver_id = pd.id
      JOIN rallies r ON e.rally_id = r.id
      LEFT JOIN overall_classification oc ON e.id = oc.entry_id
      WHERE r.name LIKE '%Stereas Elladas 2025%'
      AND e.number IN (1, 6, 101)
      ORDER BY e.number
    `);
    
    console.log('Overall Classification for test entries:');
    overallClassification.rows.forEach(row => {
      console.log(`  #${row.number}: ${row.driver} - Position: ${row.position || 'NULL'}, Status: ${row.status}, Time: ${row.total_time || 'NULL'}`);
    });
    
    // Check championship classification with detailed info
    const championshipClassification = await pool.query(`
      SELECT 
        championship_name,
        driver,
        position,
        total_time,
        championship_points,
        rally_name
      FROM championship_overall_classification_new
      WHERE rally_name LIKE '%Stereas Elladas 2025%'
      ORDER BY championship_name, position
    `);
    
    console.log('\nChampionship Classification (detailed):');
    championshipClassification.rows.forEach(row => {
      console.log(`  ${row.championship_name}: ${row.position}. ${row.driver} (${row.championship_points} pts) - Time: ${row.total_time || 'NULL'}`);
    });
    
    // Check if there are any issues with the overall_classification view
    const overallClassificationCount = await pool.query(`
      SELECT COUNT(*) as total_entries, COUNT(oc.position) as entries_with_position
      FROM entries e
      JOIN rallies r ON e.rally_id = r.id
      LEFT JOIN overall_classification oc ON e.id = oc.entry_id
      WHERE r.name LIKE '%Stereas Elladas 2025%'
    `);
    
    console.log('\nOverall Classification Stats:');
    console.log(`  Total entries: ${overallClassificationCount.rows[0].total_entries}`);
    console.log(`  Entries with position: ${overallClassificationCount.rows[0].entries_with_position}`);
    
    // Check the points calculation logic
    console.log('\nPoints calculation test:');
    for (let pos = 1; pos <= 10; pos++) {
      const points = pos === 1 ? 25 : pos === 2 ? 18 : pos === 3 ? 15 : pos === 4 ? 12 : pos === 5 ? 10 : 
                   pos === 6 ? 8 : pos === 7 ? 6 : pos === 8 ? 4 : pos === 9 ? 2 : pos === 10 ? 1 : 0;
      console.log(`  Position ${pos}: ${points} points`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error debugging championship points:', error);
    process.exit(1);
  }
}

debugChampionshipPoints();
