-- Populate Championships with Rallies
-- Execute the ready-to-run assignment queries

-- 1. Add rallies with C-classes to Greece Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
       OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'
)
ON CONFLICT DO NOTHING;

-- 2. Add rallies with numeric classes to Historic Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17'
)
ON CONFLICT DO NOTHING;

-- 3. Add rallies with numeric classes to Historic Gravel Cup
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c'
)
ON CONFLICT DO NOTHING;

-- 4. Add Rally3 cars to Rally3 Championship (if you have Rally3 entries)
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '895f159e-f147-439b-b5ab-04972033a7bb'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%Rally3%' OR e.class LIKE '%R3%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb'
)
ON CONFLICT DO NOTHING;

-- 5. Verify the assignments
SELECT 'Championship Assignment Results:' as results;

SELECT 
    c.name as championship,
    COUNT(DISTINCT ce.rally_id) as rallies_assigned,
    COUNT(DISTINCT r.country) as countries_covered
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name
ORDER BY rallies_assigned DESC;

-- 6. Show rally details per championship
SELECT 'Rally Details per Championship:' as details;
SELECT 
    c.name as championship,
    r.name as rally_name,
    r.country,
    r.start_date,
    ce.coefficient,
    COUNT(e.id) as total_entries
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
LEFT JOIN entries e ON e.rally_id = r.id
GROUP BY c.id, c.name, r.id, r.name, r.country, r.start_date, ce.coefficient
ORDER BY c.name, r.start_date;

-- 7. Test championship views with populated data
SELECT 'Testing championship_overall_classification:' as test;
SELECT 
    championship_name,
    rally_name,
    COUNT(*) as entries,
    STRING_AGG(DISTINCT class, ', ') as classes
FROM championship_overall_classification
GROUP BY championship_id, championship_name, rally_id, rally_name
ORDER BY championship_name, rally_name
LIMIT 10;

SELECT 'Testing championship_standings:' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points,
    total_power_stage_points,
    grand_total_points
FROM championship_standings
WHERE position <= 5
ORDER BY championship_name, position;

SELECT 'Testing championship_class_standings:' as test;
SELECT 
    championship_name,
    class,
    class_position,
    driver,
    rallies_completed,
    class_points
FROM championship_class_standings
WHERE class_position <= 3
ORDER BY championship_name, class, class_position
LIMIT 15;

-- 8. Summary statistics
SELECT 'Championship Summary:' as summary;
SELECT 
    c.name as championship,
    COUNT(DISTINCT ce.rally_id) as total_rallies,
    COUNT(DISTINCT coc.entry_id) as total_entries,
    COUNT(DISTINCT coc.driver) as unique_drivers,
    STRING_AGG(DISTINCT coc.class, ', ') as classes_included
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN championship_overall_classification coc ON coc.championship_id = c.id
GROUP BY c.id, c.name
ORDER BY total_entries DESC;

SELECT 'Championship population complete!' as result;
