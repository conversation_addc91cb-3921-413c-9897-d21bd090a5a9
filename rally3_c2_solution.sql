-- Solution for Rally3 cars that are in C2 class
-- We need to distinguish between regular C2 cars and Rally3 C2 cars

-- Option 1: Update Rally3 cars to have "C2, Rally3" class
-- This way they can be identified specifically for Rally3 championship

-- First, let's see all C2 entries to identify which ones are Rally3
SELECT 'All C2 entries in Rally Stereas Elladas 2025:' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (e.class = 'C2' OR e.class LIKE '%C2%')
ORDER BY e.number;

-- Option 1: Identify Rally3 cars by car model and update their class
-- You'll need to identify which C2 cars are actually Rally3 cars

-- Example: Update specific Rally3 cars to have "C2, Rally3" class
-- Replace the car names with actual Rally3 car models from your data
UPDATE entries 
SET class = 'C2, Rally3'
WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
AND (
    car LIKE '%Rally3%' 
    OR car LIKE '%Clio Rally3%'
    OR car LIKE '%208 Rally3%'
    OR car LIKE '%Corsa Rally3%'
    OR car LIKE '%Fiesta Rally3%'
    -- Add more Rally3 car models as needed
);

-- Or update by specific entry numbers if you know which C2 entries are Rally3
-- UPDATE entries 
-- SET class = 'C2, Rally3'
-- WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
-- AND number IN ('5', '12', '23');  -- Replace with actual Rally3 entry numbers

-- Update the Rally3 championship to look for "Rally3" in the class field
DELETE FROM championship_classes WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb';
INSERT INTO championship_classes (championship_id, class_name) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb', 'Rally3');

-- Option 2: Alternative approach - Create a separate rally3_entries table
-- This approach keeps the original classes but adds Rally3 designation separately

CREATE TABLE IF NOT EXISTS rally3_entries (
    id SERIAL PRIMARY KEY,
    entry_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    UNIQUE(entry_id)
);

-- Mark specific entries as Rally3 (replace with actual entry IDs)
-- INSERT INTO rally3_entries (entry_id) 
-- SELECT id FROM entries 
-- WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
-- AND number IN ('5', '12', '23');  -- Replace with actual Rally3 entry numbers

-- Update the championship view to handle Rally3 entries table (if using Option 2)
-- This would require modifying the view to check the rally3_entries table

-- For now, let's go with Option 1 (updating class to include Rally3)
-- Show the results after updating Rally3 cars
SELECT 'C2 entries after Rally3 classification:' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class,
    CASE 
        WHEN e.class LIKE '%Rally3%' THEN 'Rally3 Championship'
        WHEN e.class LIKE '%C2%' THEN 'Greece Championship'
        ELSE 'Other'
    END as championship_eligibility
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (e.class = 'C2' OR e.class LIKE '%C2%')
ORDER BY e.number;

-- Verify championship distribution
SELECT 'Championship distribution after Rally3 fix:' as info;
SELECT 
    championship_name,
    COUNT(*) as entry_count,
    STRING_AGG(DISTINCT class, ', ' ORDER BY class) as classes
FROM championship_overall_classification
WHERE rally_name LIKE '%Stereas Elladas%'
GROUP BY championship_id, championship_name
ORDER BY entry_count DESC;
