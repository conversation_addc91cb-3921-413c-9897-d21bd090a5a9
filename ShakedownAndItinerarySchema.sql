-- Shakedown table
CREATE TABLE shakedowns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  length NUMERIC(6,2) NOT NULL,
  date DATE NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  max_runs INTEGER DEFAULT 3,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Shakedown runs table - tracks individual runs by each entry
CREATE TABLE shakedown_runs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  shakedown_id UUID NOT NULL REFERENCES shakedowns(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  run_number INTEGER NOT NULL,
  time NUMERIC(10,3), -- Time in seconds, NULL if not completed
  timestamp TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(shakedown_id, entry_id, run_number)
);

-- Itinerary items table - single table approach with type discriminator
CREATE TABLE itinerary_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'shakedown', 'start', 'service', 'regroup', 'tyre_fitting', 
    'remote_service', 'refuel', 'finish', 'podium', 'stage', 'parc_ferme'
  )),
  name TEXT NOT NULL,
  location TEXT,
  start_time TIMESTAMPTZ NOT NULL,
  duration INTEGER, -- in minutes, for service, regroup, etc.
  leg_number INTEGER NOT NULL,
  day_number INTEGER NOT NULL,
  order_in_day INTEGER NOT NULL,
  related_id UUID, -- Can reference stage_id if type is 'stage'
  additional_info JSONB, -- For type-specific data
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add leg column to stages table if not already added
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'stages' AND column_name = 'leg'
  ) THEN
    ALTER TABLE stages ADD COLUMN leg_number INTEGER DEFAULT 1;
  END IF;
END $$;

-- Add day column to stages table if not already added
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'stages' AND column_name = 'day_number'
  ) THEN
    ALTER TABLE stages ADD COLUMN day_number INTEGER DEFAULT 1;
  END IF;
END $$;

-- Indexes
CREATE INDEX idx_shakedown_rally_id ON shakedowns(rally_id);
CREATE INDEX idx_shakedown_runs_shakedown_id ON shakedown_runs(shakedown_id);
CREATE INDEX idx_shakedown_runs_entry_id ON shakedown_runs(entry_id);
CREATE INDEX idx_itinerary_items_rally_id ON itinerary_items(rally_id);
CREATE INDEX idx_itinerary_items_type ON itinerary_items(type);
CREATE INDEX idx_itinerary_items_leg_day ON itinerary_items(rally_id, leg_number, day_number);