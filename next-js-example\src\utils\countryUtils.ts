// Map of country names to ISO 3166-1 alpha-2 codes for flag display
const countryCodeMap: Record<string, string> = {
  'Afghanistan': 'af',
  'Albania': 'al',
  'Algeria': 'dz',
  'Andorra': 'ad',
  'Angola': 'ao',
  'Argentina': 'ar',
  'Armenia': 'am',
  'Australia': 'au',
  'Austria': 'at',
  'Azerbaijan': 'az',
  'Bahrain': 'bh',
  'Belgium': 'be',
  'Bolivia': 'bo',
  'Bosnia and Herzegovina': 'ba',
  'Brazil': 'br',
  'Bulgaria': 'bg',
  'Canada': 'ca',
  'Chile': 'cl',
  'China': 'cn',
  'Colombia': 'co',
  'Croatia': 'hr',
  'Cyprus': 'cy',
  'Czech Republic': 'cz',
  'Denmark': 'dk',
  'Ecuador': 'ec',
  'Egypt': 'eg',
  'Estonia': 'ee',
  'Finland': 'fi',
  'France': 'fr',
  'Georgia': 'ge',
  'Germany': 'de',
  'Greece': 'gr',
  'Hungary': 'hu',
  'Iceland': 'is',
  'India': 'in',
  'Indonesia': 'id',
  'Ireland': 'ie',
  'Israel': 'il',
  'Italy': 'it',
  'Japan': 'jp',
  'Jordan': 'jo',
  'Kazakhstan': 'kz',
  'Kenya': 'ke',
  'Latvia': 'lv',
  'Lebanon': 'lb',
  'Lithuania': 'lt',
  'Luxembourg': 'lu',
  'Malaysia': 'my',
  'Malta': 'mt',
  'Mexico': 'mx',
  'Monaco': 'mc',
  'Mongolia': 'mn',
  'Montenegro': 'me',
  'Morocco': 'ma',
  'Netherlands': 'nl',
  'New Zealand': 'nz',
  'Norway': 'no',
  'Oman': 'om',
  'Paraguay': 'py',
  'Peru': 'pe',
  'Poland': 'pl',
  'Portugal': 'pt',
  'Qatar': 'qa',
  'Romania': 'ro',
  'Russia': 'ru',
  'Saudi Arabia': 'sa',
  'Serbia': 'rs',
  'Singapore': 'sg',
  'Slovakia': 'sk',
  'Slovenia': 'si',
  'South Africa': 'za',
  'South Korea': 'kr',
  'Spain': 'es',
  'Sweden': 'se',
  'Switzerland': 'ch',
  'Thailand': 'th',
  'Turkey': 'tr',
  'Ukraine': 'ua',
  'United Arab Emirates': 'ae',
  'United Kingdom': 'gb',
  'United States': 'us',
  'Uruguay': 'uy',
  'Venezuela': 've',
};

/**
 * Get the country code for a given country name
 * @param country The country name
 * @returns The ISO 3166-1 alpha-2 code or 'xx' if not found
 */
export function getCountryCode(country: string): string {
  if (!country) return 'xx';
  
  // Try direct match
  const code = countryCodeMap[country];
  if (code) return code;
  
  // Try case-insensitive match
  const lowerCountry = country.toLowerCase();
  for (const [key, value] of Object.entries(countryCodeMap)) {
    if (key.toLowerCase() === lowerCountry) {
      return value;
    }
  }
  
  // Try partial match (for abbreviated country names)
  for (const [key, value] of Object.entries(countryCodeMap)) {
    if (key.toLowerCase().includes(lowerCountry) || lowerCountry.includes(key.toLowerCase())) {
      return value;
    }
  }
  
  // Default fallback
  return 'xx';
}
