-- Check for potential name parsing issues in the persons table
-- This script helps identify persons that might have been created with incorrect name parsing

-- ============================================================================
-- STEP 1: Check for persons with multiple words in first_name (potential issue)
-- ============================================================================
SELECT 'PERSONS WITH MULTIPLE WORDS IN FIRST NAME (POTENTIAL ISSUES)' as section;
SELECT 
  id,
  first_name,
  last_name,
  nationality,
  LENGTH(first_name) - LENGTH(REPLACE(first_name, ' ', '')) + 1 as word_count_in_first_name,
  created_at
FROM persons 
WHERE first_name LIKE '% %'  -- Contains spaces in first name
ORDER BY created_at DESC, first_name;

-- ============================================================================
-- STEP 2: Check for persons with single-letter last names (potential abbreviations)
-- ============================================================================
SELECT 'PERSONS WITH SINGLE-LETTER LAST NAMES' as section;
SELECT 
  id,
  first_name,
  last_name,
  nationality,
  created_at
FROM persons 
WHERE LENGTH(TRIM(last_name)) <= 2  -- Single letter or letter with dot
ORDER BY created_at DESC, last_name;

-- ============================================================================
-- STEP 3: Check for potential duplicates (same person with different name parsing)
-- ============================================================================
SELECT 'POTENTIAL DUPLICATES (SAME NATIONALITY, SIMILAR NAMES)' as section;
SELECT 
  p1.id as id1,
  p1.first_name as first_name1,
  p1.last_name as last_name1,
  p2.id as id2,
  p2.first_name as first_name2,
  p2.last_name as last_name2,
  p1.nationality,
  p1.created_at as created1,
  p2.created_at as created2
FROM persons p1
JOIN persons p2 ON p1.nationality = p2.nationality 
  AND p1.id != p2.id
  AND (
    -- Check if first name of one matches part of the other's full name
    LOWER(p1.first_name) = LOWER(SPLIT_PART(p2.first_name || ' ' || p2.last_name, ' ', 1))
    OR LOWER(p2.first_name) = LOWER(SPLIT_PART(p1.first_name || ' ' || p1.last_name, ' ', 1))
    -- Check if last names contain similar parts
    OR LOWER(p1.last_name) LIKE '%' || LOWER(p2.last_name) || '%'
    OR LOWER(p2.last_name) LIKE '%' || LOWER(p1.last_name) || '%'
  )
ORDER BY p1.nationality, p1.first_name;

-- ============================================================================
-- STEP 4: Show recent imports (likely from EWRC) to check parsing
-- ============================================================================
SELECT 'RECENT PERSON IMPORTS (LAST 30 DAYS)' as section;
SELECT 
  id,
  first_name,
  last_name,
  nationality,
  created_at,
  CASE 
    WHEN first_name LIKE '% %' THEN 'MULTI-WORD FIRST NAME'
    WHEN LENGTH(TRIM(last_name)) <= 2 THEN 'SHORT LAST NAME'
    ELSE 'NORMAL'
  END as parsing_status
FROM persons 
WHERE created_at >= NOW() - INTERVAL '30 days'
ORDER BY created_at DESC;

-- ============================================================================
-- STEP 5: Count statistics
-- ============================================================================
SELECT 'PARSING STATISTICS' as section;
SELECT 
  COUNT(*) as total_persons,
  COUNT(CASE WHEN first_name LIKE '% %' THEN 1 END) as multi_word_first_names,
  COUNT(CASE WHEN LENGTH(TRIM(last_name)) <= 2 THEN 1 END) as short_last_names,
  COUNT(CASE WHEN first_name LIKE '% %' OR LENGTH(TRIM(last_name)) <= 2 THEN 1 END) as potential_issues,
  ROUND(
    COUNT(CASE WHEN first_name LIKE '% %' OR LENGTH(TRIM(last_name)) <= 2 THEN 1 END) * 100.0 / COUNT(*), 
    2
  ) as issue_percentage
FROM persons;
