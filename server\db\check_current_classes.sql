-- Check Current Class Data
-- Run this first to see what class combinations you currently have

-- See all unique class combinations and how many times they're used
SELECT 
  class,
  COUNT(*) as usage_count,
  STRING_AGG(DISTINCT CONCAT('Entry #', number), ', ' ORDER BY number) as example_entries
FROM entries 
WHERE class IS NOT NULL AND class != ''
GROUP BY class
ORDER BY usage_count DESC, class;

-- Show some example entries with their current classes
SELECT 
  id,
  number,
  class,
  CASE 
    WHEN class LIKE '% %' AND class NOT LIKE '%,%' THEN 'NEEDS CONVERSION (has spaces, no commas)'
    WHEN class LIKE '%,%' THEN 'ALREADY COMMA-SEPARATED'
    ELSE 'SINGLE CLASS'
  END as status
FROM entries 
WHERE class IS NOT NULL AND class != ''
ORDER BY 
  CASE 
    WHEN class LIKE '% %' AND class NOT LIKE '%,%' THEN 1  -- Needs conversion first
    WHEN class LIKE '%,%' THEN 2                          -- Already converted
    ELSE 3                                                -- Single class
  END,
  number
LIMIT 50;
