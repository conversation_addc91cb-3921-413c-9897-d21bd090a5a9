import pool from './server/config/db.js';
import fs from 'fs';

async function testChampionshipAssociations() {
  const client = await pool.connect();
  
  try {
    console.log('=== Testing Championship Associations ===');
    
    // Read JSON file
    const jsonContent = fs.readFileSync('./ewrc_complete_results_94918-rally-stereas-elladas-2025.json', 'utf8');
    const ewrcData = JSON.parse(jsonContent);
    
    console.log('Loaded EWRC data with', ewrcData.results.length, 'results');
    
    // Get rally ID
    const rally = await client.query(
      "SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%'",
    );
    
    if (rally.rows.length === 0) {
      console.error('Rally not found');
      process.exit(1);
    }
    
    const rallyId = rally.rows[0].id;
    console.log('Rally ID:', rallyId);
    
    // Process a few sample entries to test championship associations
    const sampleEntries = [1, 6, 101]; // Different championship combinations
    
    for (const entryNumber of sampleEntries) {
      console.log(`\n--- Processing Entry #${entryNumber} ---`);
      
      // Find this entry in the JSON data
      const entryResults = ewrcData.results.filter(r => 
        parseInt(r.entry_number.replace('#', '')) === entryNumber
      );
      
      if (entryResults.length === 0) {
        console.log(`Entry #${entryNumber} not found in JSON data`);
        continue;
      }
      
      const firstResult = entryResults[0];
      console.log('Driver/Codriver:', firstResult.driver_codriver);
      console.log('Car:', firstResult.car);
      console.log('Classes:', firstResult.group);
      console.log('Championships:', firstResult.championship);
      
      // Get entry from database
      const entryQuery = await client.query(
        'SELECT id FROM entries WHERE rally_id = $1 AND number = $2',
        [rallyId, entryNumber]
      );
      
      if (entryQuery.rows.length === 0) {
        console.log(`Entry #${entryNumber} not found in database`);
        continue;
      }
      
      const entryId = entryQuery.rows[0].id;
      console.log('Entry ID:', entryId);
      
      // Process championship associations
      if (firstResult.championship && Array.isArray(firstResult.championship)) {
        console.log('Processing championships:', firstResult.championship);
        
        // Clear existing associations
        await client.query('DELETE FROM entry_championships WHERE entry_id = $1', [entryId]);
        
        for (const championshipName of firstResult.championship) {
          console.log(`  Looking for championship: "${championshipName}"`);
          
          // Find championship using the same logic as in the import
          let existingChampionship = await client.query(
            'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
            [championshipName.trim()]
          );

          // If not found, try without (GR) suffix
          if (existingChampionship.rows.length === 0 && championshipName.includes('(GR)')) {
            const nameWithoutGR = championshipName.replace(/\s*\(GR\)\s*/gi, '').trim();
            console.log(`    Trying without (GR): "${nameWithoutGR}"`);
            existingChampionship = await client.query(
              'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
              [nameWithoutGR]
            );
          }

          // If still not found, try partial match
          if (existingChampionship.rows.length === 0) {
            const mainWord = championshipName.replace(/\s*\(GR\)\s*/gi, '').split(' ')[0].trim();
            if (mainWord.length > 2) {
              console.log(`    Trying partial match: "%${mainWord}%"`);
              existingChampionship = await client.query(
                'SELECT id, name FROM championships WHERE LOWER(name) LIKE LOWER($1)',
                [`%${mainWord}%`]
              );
            }
          }

          if (existingChampionship.rows.length > 0) {
            const championshipId = existingChampionship.rows[0].id;
            const actualName = existingChampionship.rows[0].name;

            // Insert entry-championship association
            await client.query(
              'INSERT INTO entry_championships (entry_id, championship_id) VALUES ($1, $2) ON CONFLICT (entry_id, championship_id) DO NOTHING',
              [entryId, championshipId]
            );

            console.log(`    ✅ Associated with: "${actualName}" (ID: ${championshipId})`);
          } else {
            console.log(`    ❌ Championship not found: "${championshipName}"`);
          }
        }
      }
    }
    
    // Check results
    console.log('\n=== Results ===');
    const associations = await client.query(`
      SELECT 
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        c.name as championship
      FROM entry_championships ec
      JOIN entries e ON ec.entry_id = e.id
      JOIN championships c ON ec.championship_id = c.id
      JOIN persons pd ON e.driver_id = pd.id
      WHERE e.rally_id = $1 AND e.number = ANY($2)
      ORDER BY e.number, c.name
    `, [rallyId, sampleEntries]);
    
    console.log(`Created ${associations.rows.length} championship associations:`);
    associations.rows.forEach(row => {
      console.log(`  Entry #${row.number} (${row.driver}) → ${row.championship}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing championship associations:', error);
    process.exit(1);
  } finally {
    client.release();
  }
}

testChampionshipAssociations();
