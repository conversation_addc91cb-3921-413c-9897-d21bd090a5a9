-- Test Championship Integration
-- Run these queries to verify the championship system is working

-- 1. Check if championship classes are set up
SELECT 'Championship Classes Setup:' as test;
SELECT 
    c.name as championship,
    cc.class_pattern,
    cc.match_type,
    cc.description
FROM championship_classes cc
JOIN championships c ON cc.championship_id = c.id
ORDER BY c.name, cc.class_pattern;

-- 2. Check championship results (should show filtered entries)
SELECT 'Championship Results Sample:' as test;
SELECT 
    cr.championship_name,
    cr.rally_name,
    cr.driver,
    cr.class,
    cr.championship_position,
    cr.points
FROM championship_results cr
ORDER BY cr.championship_name, cr.rally_name, cr.championship_position
LIMIT 20;

-- 3. Check championship standings
SELECT 'Championship Standings:' as test;
SELECT 
    cs.championship_name,
    cs.championship_position,
    cs.driver,
    cs.total_points,
    cs.rallies_participated,
    cs.wins,
    cs.podiums
FROM championship_standings cs
WHERE cs.championship_position <= 5
ORDER BY cs.championship_name, cs.championship_position;

-- 4. Check which rallies are in championships
SELECT 'Rallies per Championship:' as test;
SELECT 
    c.name as championship,
    COUNT(DISTINCT r.id) as total_rallies,
    STRING_AGG(r.name, ', ' ORDER BY r.start_date) as rally_names
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name
ORDER BY total_rallies DESC;

-- 5. Check class distribution
SELECT 'Class Distribution:' as test;
SELECT 
    cr.championship_name,
    cr.class,
    COUNT(*) as entry_count,
    COUNT(DISTINCT cr.driver) as unique_drivers
FROM championship_results cr
GROUP BY cr.championship_id, cr.championship_name, cr.class
ORDER BY cr.championship_name, entry_count DESC;

-- 6. Test API endpoints (these should work in your application)
SELECT 'API Test Queries:' as test;

-- Test rally championships endpoint
SELECT 'Rally Championships (for API /api/championship-results/rallies/:rallyId/championships):' as endpoint;
SELECT 
    ce.championship_id,
    c.name AS championship_name,
    c.type AS championship_type,
    c.year,
    ce.coefficient,
    ce.created_at
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
WHERE ce.rally_id = (SELECT id FROM rallies LIMIT 1)
ORDER BY c.name;

-- Test championship standings endpoint
SELECT 'Championship Standings (for API /api/championship-results/championships/:championshipId/standings):' as endpoint;
SELECT 
    cs.championship_id,
    cs.championship_name,
    cs.driver,
    cs.driver_nationality,
    cs.rallies_participated,
    cs.total_points,
    cs.average_points,
    cs.best_result_points,
    cs.best_position,
    cs.wins,
    cs.podiums,
    cs.points_finishes,
    cs.championship_position as position
FROM championship_standings cs
WHERE cs.championship_id = (SELECT id FROM championships LIMIT 1)
ORDER BY cs.championship_position
LIMIT 10;
