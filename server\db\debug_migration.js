import pool from '../config/db.js';

async function debugMigration() {
  console.log('🔍 Debugging Migration Issues...\n');

  try {
    // Test database connection
    console.log('1. Testing database connection...');
    await pool.query('SELECT 1');
    console.log('✅ Database connection successful');

    // Check if results table exists
    console.log('\n2. Checking if results table exists...');
    const resultsTable = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'results'
    `);
    
    if (resultsTable.rows.length === 0) {
      console.log('❌ Results table does not exist');
      return;
    }
    console.log('✅ Results table exists');

    // Check if entries table exists
    console.log('\n3. Checking if entries table exists...');
    const entriesTable = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'entries'
    `);
    
    if (entriesTable.rows.length === 0) {
      console.log('❌ Entries table does not exist');
      return;
    }
    console.log('✅ Entries table exists');

    // Check current schema of results table
    console.log('\n4. Checking current results table schema...');
    const resultsSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'results'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Results table columns:');
    resultsSchema.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check if is_active column already exists
    const isActiveExists = resultsSchema.rows.some(col => col.column_name === 'is_active');
    console.log(`\n5. is_active column exists: ${isActiveExists ? '✅ Yes' : '❌ No'}`);

    // Check if entry_status_history table exists
    console.log('\n6. Checking if entry_status_history table exists...');
    const historyTable = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'entry_status_history'
    `);
    console.log(`   entry_status_history table exists: ${historyTable.rows.length > 0 ? '✅ Yes' : '❌ No'}`);

    // Check entries table schema for status column
    console.log('\n7. Checking entries table status column...');
    const entriesSchema = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'entries' AND column_name = 'status'
    `);
    
    if (entriesSchema.rows.length > 0) {
      console.log('✅ Entries status column exists:', entriesSchema.rows[0]);
    } else {
      console.log('❌ Entries status column does not exist');
    }

    // Check if uuid extension is available
    console.log('\n8. Checking UUID extension...');
    const uuidExtension = await pool.query(`
      SELECT extname 
      FROM pg_extension 
      WHERE extname = 'uuid-ossp'
    `);
    console.log(`   uuid-ossp extension: ${uuidExtension.rows.length > 0 ? '✅ Available' : '❌ Not available'}`);

    // Try to create uuid if extension not available
    if (uuidExtension.rows.length === 0) {
      try {
        await pool.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
        console.log('✅ UUID extension created successfully');
      } catch (uuidError) {
        console.log('⚠️  UUID extension creation failed:', uuidError.message);
      }
    }

    console.log('\n📊 Debug Summary Complete');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the debug
debugMigration()
  .then(() => {
    console.log('\n✨ Debug completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Debug failed:', error.message);
    process.exit(1);
  });
