-- Simple version: Create function to track entry status changes
-- Using basic $$ syntax to avoid any quoting issues

CREATE OR REPLACE FUNCTION track_entry_status_change()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
  -- Only track if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO entry_status_history (
      entry_id, 
      rally_id, 
      old_status, 
      new_status,
      reason
    ) VALUES (
      NEW.id,
      NEW.rally_id,
      OLD.status,
      NEW.status,
      CASE 
        WHEN NEW.status IN ('retired', 'dns', 'dnf', 'dsq') THEN 'Entry marked as ' || NEW.status
        ELSE 'Status updated to ' || NEW.status
      END
    );
  END IF;
  
  RETURN NEW;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_track_entry_status_change ON entries;

-- Create trigger to automatically track entry status changes
CREATE TRIGGER trigger_track_entry_status_change
  AFTER UPDATE ON entries
  FOR EACH ROW
  EXECUTE FUNCTION track_entry_status_change();
