-- Verification script to check if names are correctly parsed after re-import
-- Run this AFTER re-importing your EWRC data

SELECT 'NAME PARSING VERIFICATION' as section;

-- Check sample of persons to see if names look correct
SELECT 'SAMPLE PERSONS (should show FirstName LastName format)' as subsection;
SELECT 
  id,
  first_name,
  last_name,
  nationality,
  first_name || ' ' || last_name as full_name_display,
  created_at
FROM persons 
ORDER BY created_at DESC
LIMIT 20;

-- Check for any obvious parsing issues
SELECT 'POTENTIAL PARSING ISSUES' as subsection;
SELECT 
  COUNT(*) as total_persons,
  COUNT(CASE WHEN first_name LIKE '% %' THEN 1 END) as multi_word_first_names,
  COUNT(CASE WHEN LENGTH(TRIM(last_name)) <= 2 THEN 1 END) as very_short_last_names,
  COUNT(CASE WHEN LENGTH(TRIM(first_name)) <= 2 THEN 1 END) as very_short_first_names
FROM persons;

-- Show some specific examples that should be correct now
SELECT 'SPECIFIC EXAMPLES (Greek names should be FirstName LastName)' as subsection;
SELECT 
  first_name,
  last_name,
  nationality,
  first_name || ' ' || last_name as display_name
FROM persons 
WHERE nationality IN ('greece', 'cyprus')
  AND (
    last_name LIKE '%poulos%' OR 
    last_name LIKE '%akis%' OR 
    last_name LIKE '%dimitriou%' OR
    last_name LIKE '%opoulos%'
  )
ORDER BY last_name
LIMIT 10;

-- Check entries to see driver/codriver names
SELECT 'SAMPLE ENTRIES WITH DRIVER/CODRIVER NAMES' as subsection;
SELECT 
  e.number,
  d.first_name || ' ' || d.last_name as driver_name,
  c.first_name || ' ' || c.last_name as codriver_name,
  e.car,
  r.name as rally_name
FROM entries e
JOIN persons d ON e.driver_id = d.id
JOIN persons c ON e.codriver_id = c.id
JOIN rallies r ON e.rally_id = r.id
ORDER BY e.number
LIMIT 10;
