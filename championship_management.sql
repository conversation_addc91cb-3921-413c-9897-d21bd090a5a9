-- Championship Management Scripts
-- Easy commands for managing championships across all rallies

-- ============================================================================
-- 1. ADD NEW CHAMPIONSHIP
-- ============================================================================

-- Template for adding a new championship
/*
-- Step 1: Add the championship
INSERT INTO championships (id, name, year, description) VALUES
('new-championship-id', 'Championship Name', 2025, 'Description');

-- Step 2: Add class rules
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('new-championship-id', 'WRC', 'contains', 'WRC cars'),
('new-championship-id', 'Rally1', 'contains', 'Rally1 cars');

-- Step 3: Add rallies to championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT 'new-championship-id', id, 1.0
FROM rallies
WHERE country = 'Greece' AND YEAR(start_date) = 2025;

-- Step 4: Set custom points (optional)
INSERT INTO championship_points_system (championship_id, position, points) VALUES
('new-championship-id', 1, 25), ('new-championship-id', 2, 18), ('new-championship-id', 3, 15);
*/

-- ============================================================================
-- 2. ADD RALLY TO EXISTING CHAMPIONSHIPS
-- ============================================================================

-- Template for adding a new rally to appropriate championships
/*
-- Add rally to Greece championship (if it's a Greek rally with C-classes)
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT '55a003a9-66ff-4a37-b11d-2e14df10bae3', 'new-rally-id', 1.0
WHERE EXISTS (
    SELECT 1 FROM entries e
    WHERE e.rally_id = 'new-rally-id'
    AND (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%'
         OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%')
);

-- Add rally to Historic championship (if it has numeric classes)
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT 'cc1e9147-fdb3-4483-aad3-9e27795eff17', 'new-rally-id', 1.0
WHERE EXISTS (
    SELECT 1 FROM entries e
    WHERE e.rally_id = 'new-rally-id'
    AND e.class IN ('1', '2', '3', '4')
);
*/

-- ============================================================================
-- 3. AUTOMATIC RALLY ASSIGNMENT BASED ON CLASSES
-- ============================================================================

-- Automatically add rallies to championships based on their entry classes
-- Simple SQL approach for PostgreSQL

-- Add rallies with C-classes to Greece Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, r.id, 1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%'
       OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events
    WHERE championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid
);

-- Add rallies with numeric classes to Historic Championships
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, r.id, 1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('1', '2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events
    WHERE championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid
);

INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, r.id, 1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('1', '2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events
    WHERE championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid
);

-- Add rallies with Rally3 cars to Rally3 Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT '895f159e-f147-439b-b5ab-04972033a7bb'::uuid, r.id, 1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%Rally3%' OR e.class LIKE '%R3%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events
    WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb'::uuid
);

-- ============================================================================
-- 4. USEFUL QUERIES FOR CHAMPIONSHIP MANAGEMENT
-- ============================================================================

-- Find rallies not assigned to any championship
SELECT 'Rallies not in any championship:' as info;
SELECT
    r.id,
    r.name,
    r.country,
    r.start_date,
    STRING_AGG(DISTINCT e.class, ', ' ORDER BY e.class) as classes_present
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE r.id NOT IN (SELECT DISTINCT rally_id FROM championship_events)
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC;

-- Find championships with no rallies
SELECT 'Championships with no rallies:' as info;
SELECT
    c.id,
    c.name,
    c.year
FROM championships c
WHERE c.id NOT IN (SELECT DISTINCT championship_id FROM championship_events)
ORDER BY c.year DESC, c.name;

-- Show championship coverage
SELECT 'Championship coverage summary:' as info;
SELECT
    c.name as championship,
    COUNT(DISTINCT ce.rally_id) as rallies_included,
    COUNT(DISTINCT r.country) as countries_covered,
    MIN(r.start_date) as earliest_rally,
    MAX(r.start_date) as latest_rally,
    STRING_AGG(DISTINCT r.country, ', ' ORDER BY r.country) as countries
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name
ORDER BY rallies_included DESC;

-- Find potential class conflicts
SELECT 'Potential class conflicts:' as info;
SELECT
    e.class,
    COUNT(DISTINCT ce.championship_id) as championship_count,
    STRING_AGG(DISTINCT c.name, ', ' ORDER BY c.name) as championships
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN championship_events ce ON r.id = ce.rally_id
JOIN championships c ON ce.championship_id = c.id
WHERE e.class IS NOT NULL
GROUP BY e.class
HAVING championship_count > 1
ORDER BY championship_count DESC;

-- ============================================================================
-- 5. QUICK SETUP COMMANDS
-- ============================================================================

-- Run auto-assignment for all rallies
-- CALL auto_assign_rallies_to_championships();

-- Add all Greek rallies to Greece championship
/*
INSERT IGNORE INTO championship_events (championship_id, rally_id, coefficient)
SELECT '55a003a9-66ff-4a37-b11d-2e14df10bae3', r.id, 1.0
FROM rallies r
WHERE r.country = 'Greece';
*/

-- Add all rallies from specific year to a championship
/*
INSERT IGNORE INTO championship_events (championship_id, rally_id, coefficient)
SELECT 'championship-id', r.id, 1.0
FROM rallies r
WHERE YEAR(r.start_date) = 2025;
*/

-- ============================================================================
-- 6. TESTING AND VALIDATION
-- ============================================================================

-- Test championship results for specific rally
/*
SELECT * FROM championship_results
WHERE rally_name = 'Rally Name'
ORDER BY championship_name, championship_position;
*/

-- Test championship standings
/*
SELECT * FROM championship_standings
WHERE championship_name = 'Championship Name'
ORDER BY championship_position;
*/

-- Validate class matching
/*
SELECT
    e.class,
    cc.class_pattern,
    cc.match_type,
    CASE
        WHEN cc.match_type = 'exact' AND e.class = cc.class_pattern THEN 'MATCH'
        WHEN cc.match_type = 'contains' AND e.class LIKE CONCAT('%', cc.class_pattern, '%') THEN 'MATCH'
        ELSE 'NO MATCH'
    END as match_result
FROM entries e
CROSS JOIN championship_classes cc
WHERE e.rally_id = 'specific-rally-id'
AND cc.championship_id = 'specific-championship-id'
ORDER BY e.class, cc.class_pattern;
*/
