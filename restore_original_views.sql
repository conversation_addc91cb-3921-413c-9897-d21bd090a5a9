-- Restore Original Championship Views
-- These were accidentally dropped by CASCADE

-- 1. Recreate championship_standings view (from your original schema)
CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- 2. Recreate championship_class_standings view (from your original schema)
CREATE VIEW championship_class_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.class,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS class_points,
  RANK() OVER (
    PARTITION BY cp.championship_id, cp.class
    ORDER BY SUM(cp.rally_points) DESC
  ) AS class_position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    e.class,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id, e.class
) psp_total ON psp_total.championship_id = cp.championship_id
  AND psp_total.driver_id = cp.driver_id
  AND psp_total.class = cp.class
GROUP BY cp.championship_id, cp.championship_name, cp.class, cp.driver_id, cp.driver;

-- 3. Verify all views are restored
SELECT 'Championship views restored:' as status;
SELECT schemaname, viewname 
FROM pg_views 
WHERE viewname LIKE '%championship%' 
ORDER BY viewname;

-- 4. Test the restored views
SELECT 'Testing championship_standings:' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points
FROM championship_standings 
WHERE position <= 3
ORDER BY championship_name, position
LIMIT 10;

SELECT 'Testing championship_class_standings:' as test;
SELECT 
    championship_name,
    class,
    class_position,
    driver,
    class_points
FROM championship_class_standings 
WHERE class_position <= 3
ORDER BY championship_name, class, class_position
LIMIT 10;

SELECT 'Original schema fully restored!' as result;
