# Next.js Migration Guide for Stagetime

This guide outlines the steps to migrate the Stagetime application from React Router to Next.js.

## Initial Setup

1. Create a new Next.js project:

```bash
npx create-next-app@latest stagetime-next --typescript --eslint
cd stagetime-next
```

2. Install dependencies:

```bash
npm install @supabase/supabase-js lucide-react tailwindcss postcss autoprefixer
# Add any other dependencies from your current project
```

3. Configure Tailwind CSS:

```bash
npx tailwindcss init -p
```

## Project Structure

Create the following directory structure:

```
src/
├── app/
│   ├── layout.tsx (Main layout)
│   ├── page.tsx (Homepage)
│   ├── admin/
│   │   ├── layout.tsx (Admin layout)
│   │   ├── page.tsx (Admin dashboard)
│   │   ├── rallies/
│   │   │   ├── page.tsx (Rallies management)
│   │   │   ├── add/
│   │   │   │   └── page.tsx (Add rally)
│   │   │   └── [id]/
│   │   │       └── page.tsx (Edit rally)
│   │   └── persons/
│   │       ├── page.tsx (Persons management)
│   │       ├── add/
│   │       │   └── page.tsx (Add person)
│   │       └── [id]/
│   │           └── page.tsx (Edit person)
├── components/
│   ├── ui/ (UI components)
│   └── admin/ (Admin components)
├── lib/
│   └── supabase/ (Database connection)
└── types/
    └── index.ts (Type definitions)
```

## Authentication Setup

1. Create Supabase client:

```typescript
// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
```

2. Create server-side Supabase client:

```typescript
// src/lib/supabase/server.ts
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export const createClient = () => {
  return createServerComponentClient({ cookies });
};
```

## Migration Strategy

1. Start with basic layouts and authentication
2. Migrate one admin section at a time (start with Persons)
3. Implement API routes for each section
4. Migrate public pages last

## Testing

After each migration step:
1. Test the migrated functionality
2. Compare with the original application
3. Fix any issues before proceeding to the next step

## Deployment

1. Set up environment variables in your deployment platform
2. Deploy the Next.js application
3. Test the deployed application
4. Switch over when ready
