-- Script to verify and fix championship entries
-- This helps ensure entries appear correctly in championship standings

-- 1. Check which entries appear in each championship
SELECT 
    c.name as championship,
    r.name as rally,
    e.number,
    CONCAT(p1.first_name, ' ', p1.last_name) as driver,
    e.class,
    e.status,
    ce.coefficient
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons p1 ON e.driver_id = p1.id
WHERE c.id = 'your-championship-id'  -- Replace with actual championship ID
ORDER BY r.start_date, e.number;

-- 2. Find entries with missing or incorrect classes
SELECT 
    r.name as rally,
    e.number,
    CONCAT(p1.first_name, ' ', p1.last_name) as driver,
    e.class,
    'Missing WRC class' as issue
FROM entries e
JOIN persons p1 ON e.driver_id = p1.id
JOIN rallies r ON e.rally_id = r.id
JOIN championship_events ce ON r.id = ce.rally_id
WHERE ce.championship_id = 'wrc-2024'  -- Replace with WRC championship ID
AND (e.class IS NULL OR e.class NOT LIKE '%WRC%')
AND p1.last_name IN ('Ogier', '<PERSON>', 'Neuville', 'Tanak', 'Rovanpera');  -- Known WRC drivers

-- 3. Find entries with incorrect status
SELECT 
    r.name as rally,
    e.number,
    CONCAT(p1.first_name, ' ', p1.last_name) as driver,
    e.status,
    'Check status' as issue
FROM entries e
JOIN persons p1 ON e.driver_id = p1.id
JOIN rallies r ON e.rally_id = r.id
JOIN championship_events ce ON r.id = ce.rally_id
WHERE ce.championship_id = 'your-championship-id'
AND e.status NOT IN ('finished', 'retired', 'dnf', 'dns', 'dsq')
ORDER BY r.start_date, e.number;

-- 4. Check for duplicate entries (same driver in same rally)
SELECT 
    r.name as rally,
    CONCAT(p1.first_name, ' ', p1.last_name) as driver,
    COUNT(*) as entry_count
FROM entries e
JOIN persons p1 ON e.driver_id = p1.id
JOIN rallies r ON e.rally_id = r.id
JOIN championship_events ce ON r.id = ce.rally_id
WHERE ce.championship_id = 'your-championship-id'
GROUP BY r.id, p1.id, p1.first_name, p1.last_name
HAVING COUNT(*) > 1;

-- 5. Verify championship points calculation
SELECT 
    driver,
    rally_name,
    position,
    base_points,
    coefficient,
    championship_points,
    class
FROM championship_overall_classification
WHERE championship_id = 'your-championship-id'
ORDER BY rally_date DESC, position;
