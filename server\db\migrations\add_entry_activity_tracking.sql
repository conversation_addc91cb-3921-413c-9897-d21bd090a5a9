-- Migration: Add entry activity tracking to results table
-- This allows tracking when entries become active/inactive during a rally

-- Add is_active column to results table to track if entry was active when this result was recorded
ALTER TABLE results ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

-- Add index for performance on activity queries
CREATE INDEX idx_results_is_active ON results(is_active);
CREATE INDEX idx_results_entry_active ON results(entry_id, is_active);

-- Create a table to track entry status changes over time
CREATE TABLE entry_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID REFERENCES stages(id) ON DELETE CASCADE, -- NULL for overall status changes
  old_status entry_status,
  new_status entry_status NOT NULL,
  changed_at TIMESTAMPTZ DEFAULT NOW(),
  changed_by UUID REFERENCES users(id), -- NULL for system changes
  reason TEXT, -- Optional reason for status change
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for entry status history
CREATE INDEX idx_entry_status_history_entry_id ON entry_status_history(entry_id);
CREATE INDEX idx_entry_status_history_rally_id ON entry_status_history(rally_id);
CREATE INDEX idx_entry_status_history_stage_id ON entry_status_history(stage_id);
CREATE INDEX idx_entry_status_history_changed_at ON entry_status_history(changed_at);

-- Create function to track entry status changes
CREATE OR REPLACE FUNCTION track_entry_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only track if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO entry_status_history (
      entry_id, 
      rally_id, 
      old_status, 
      new_status,
      reason
    ) VALUES (
      NEW.id,
      NEW.rally_id,
      OLD.status,
      NEW.status,
      CASE 
        WHEN NEW.status IN ('retired', 'dns', 'dnf', 'dsq') THEN 'Entry marked as ' || NEW.status
        ELSE 'Status updated to ' || NEW.status
      END
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically track entry status changes
CREATE TRIGGER trigger_track_entry_status_change
  AFTER UPDATE ON entries
  FOR EACH ROW
  EXECUTE FUNCTION track_entry_status_change();

-- Update existing results to mark them as active based on entry status at time of result
-- This is a one-time update for existing data
UPDATE results 
SET is_active = (
  SELECT CASE 
    WHEN e.status IN ('retired', 'dns', 'dnf', 'dsq') THEN FALSE
    ELSE TRUE
  END
  FROM entries e 
  WHERE e.id = results.entry_id
);

-- Add comment to document the new column
COMMENT ON COLUMN results.is_active IS 'Tracks if the entry was active when this result was recorded';
COMMENT ON TABLE entry_status_history IS 'Tracks all status changes for entries throughout rallies';

-- Create view for active entries in current rally
CREATE VIEW active_entries AS
SELECT 
  e.*,
  pd.first_name AS driver_first_name,
  pd.last_name AS driver_last_name,
  pd.nationality AS driver_nationality,
  pcd.first_name AS codriver_first_name,
  pcd.last_name AS codriver_last_name,
  pcd.nationality AS codriver_nationality,
  t.name AS team_name
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN teams t ON t.id = e.team_id
WHERE e.status NOT IN ('retired', 'dns', 'dnf', 'dsq');

-- Create view for retired entries
CREATE VIEW retired_entries AS
SELECT 
  e.*,
  pd.first_name AS driver_first_name,
  pd.last_name AS driver_last_name,
  pd.nationality AS driver_nationality,
  pcd.first_name AS codriver_first_name,
  pcd.last_name AS codriver_last_name,
  pcd.nationality AS codriver_nationality,
  t.name AS team_name,
  esh.changed_at AS retired_at,
  esh.reason AS retirement_reason
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN teams t ON t.id = e.team_id
LEFT JOIN entry_status_history esh ON esh.entry_id = e.id 
  AND esh.new_status = e.status 
  AND esh.new_status IN ('retired', 'dns', 'dnf', 'dsq')
WHERE e.status IN ('retired', 'dns', 'dnf', 'dsq');
