-- Backup script before dropping rallies and persons
-- Run this BEFORE doing any cleanup to have a safety backup

-- Create backup tables
CREATE TABLE IF NOT EXISTS backup_persons AS SELECT * FROM persons;
CREATE TABLE IF NOT EXISTS backup_rallies AS SELECT * FROM rallies;
CREATE TABLE IF NOT EXISTS backup_entries AS SELECT * FROM entries;
CREATE TABLE IF NOT EXISTS backup_results AS SELECT * FROM results;
CREATE TABLE IF NOT EXISTS backup_stages AS SELECT * FROM stages;
CREATE TABLE IF NOT EXISTS backup_drivers AS SELECT * FROM drivers;
CREATE TABLE IF NOT EXISTS backup_codrivers AS SELECT * FROM codrivers;
CREATE TABLE IF NOT EXISTS backup_penalties AS SELECT * FROM penalties;
CREATE TABLE IF NOT EXISTS backup_championship_events AS SELECT * FROM championship_events;

-- Show what was backed up
SELECT 'BACKUP SUMMARY' as section;
SELECT 
  'persons' as table_name, 
  (SELECT COUNT(*) FROM backup_persons) as backed_up_records,
  (SELECT COUNT(*) FROM persons) as original_records;

SELECT 
  'rallies' as table_name, 
  (SELECT COUNT(*) FROM backup_rallies) as backed_up_records,
  (SELECT COUNT(*) FROM rallies) as original_records;

SELECT 
  'entries' as table_name, 
  (SELECT COUNT(*) FROM backup_entries) as backed_up_records,
  (SELECT COUNT(*) FROM entries) as original_records;

SELECT 
  'results' as table_name, 
  (SELECT COUNT(*) FROM backup_results) as backed_up_records,
  (SELECT COUNT(*) FROM results) as original_records;

SELECT 'BACKUP COMPLETED SUCCESSFULLY' as status;
