import pool from './server/config/db.js';

async function checkExistingData() {
  try {
    console.log('=== Checking Existing Data ===');
    
    // Check rallies
    const rallies = await pool.query(`
      SELECT id, name, start_date, country
      FROM rallies
      WHERE name LIKE '%Stereas Elladas 2025%'
      ORDER BY start_date DESC
    `);
    
    console.log(`Found ${rallies.rows.length} rallies:`);
    rallies.rows.forEach(row => {
      console.log(`  ${row.name} (${row.start_date}) - ID: ${row.id}`);
    });
    
    if (rallies.rows.length > 0) {
      const rallyId = rallies.rows[0].id;
      
      // Check entries
      const entries = await pool.query(`
        SELECT 
          e.id, e.number, e.class, e.status,
          CONCAT(pd.first_name, ' ', pd.last_name) as driver
        FROM entries e
        JOIN persons pd ON e.driver_id = pd.id
        WHERE e.rally_id = $1
        ORDER BY e.number
        LIMIT 10
      `, [rallyId]);
      
      console.log(`\nFound ${entries.rows.length} entries for this rally (showing first 10):`);
      entries.rows.forEach(row => {
        console.log(`  #${row.number}: ${row.driver} (${row.class}) - ${row.status}`);
      });
      
      // Check championships
      const championships = await pool.query(`
        SELECT id, name, year, type
        FROM championships
        ORDER BY name
      `);
      
      console.log(`\nFound ${championships.rows.length} championships:`);
      championships.rows.forEach(row => {
        console.log(`  ${row.name} (${row.year}) - ${row.type} - ID: ${row.id}`);
      });
      
      // Check championship events
      const championshipEvents = await pool.query(`
        SELECT 
          ce.championship_id,
          c.name as championship_name,
          ce.coefficient
        FROM championship_events ce
        JOIN championships c ON ce.championship_id = c.id
        WHERE ce.rally_id = $1
      `, [rallyId]);
      
      console.log(`\nFound ${championshipEvents.rows.length} championship events for this rally:`);
      championshipEvents.rows.forEach(row => {
        console.log(`  ${row.championship_name} (coefficient: ${row.coefficient})`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking existing data:', error);
    process.exit(1);
  }
}

checkExistingData();
