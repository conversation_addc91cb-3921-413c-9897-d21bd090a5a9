# Database Schema Documentation

## Overview
This document describes the complete database schema for the Stagetime Rally Management System. The schema has been consolidated into a single authoritative file: `StageTimeSchema.sql`.

## Schema File Structure

### Main Schema File
- **`StageTimeSchema.sql`** - Complete, up-to-date database schema including all tables, views, and indexes

### Utility Files
- **`check_database_vs_schema.sql`** - Comprehensive database structure verification script
- **`server/db/check_schema.sql`** - Basic schema checking utilities

### Backup Files
All experimental, debug, and unused SQL files have been moved to `sql_backup/` folder for reference.

## Database Tables

### Core Tables

#### Users
- Admin and editor authentication
- Roles: 'admin', 'editor'

#### Persons
- Central table for all people (drivers, codrivers)
- Contains personal information, nationality, photos

#### Drivers/Codrivers
- Reference tables linking to persons
- Allows same person to be both driver and codriver

#### Championships
- Championship definitions with year and type
- Types: 'international', 'national', 'local'

#### Rallies
- Rally events with dates, status, location
- Status: 'upcoming', 'running', 'finished'

#### Stages
- Individual rally stages with timing and status
- Includes leg/day organization
- Status: 'upcoming', 'running', 'finished', 'canceled'

#### Teams
- Team information and logos

#### Entries
- Rally participants (driver + codriver + car)
- Multiple classes supported (comma-separated)
- Status tracking: 'entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq'

### Results & Timing

#### Results
- Stage times for each entry
- Includes activity tracking (`is_active`)
- Nominal time and Super Rally flags

#### Penalties
- Time penalties with reasons
- Can be stage-specific or general

#### Splits
- Intermediate timing points (future feature)

### Championship System

#### Championship Events
- Links rallies to championships
- Coefficient support for point multipliers

#### Power Stage Points
- Additional points for power stages

### Additional Features

#### Entry Status History
- Tracks all status changes with timestamps
- Includes reason for changes

#### Shakedowns
- Pre-rally testing sessions
- Multiple runs per entry supported

#### Shakedown Runs
- Individual shakedown attempts

#### Itinerary Items
- Rally schedule management
- Supports various event types: stages, service, regroup, etc.

#### News
- News articles with author tracking

## Database Views

### Overall Classification
- Complete rally standings excluding retired entries
- Includes penalties and time differences

### Championship Views
- Championship standings with points calculation
- Class-specific championship standings
- Points system: 25-18-15-12-10-8-6-4-2-1 for top 10

### Stage Results Views
- Current stage results
- Stage winners
- Power stage classifications

### Driver Statistics
- Rally participation counts
- Wins and podium statistics
- Stage win counts

## Key Features

### Multi-Class Support
- Entries can have multiple classes (comma-separated)
- Championship filtering by class

### Status Tracking
- Comprehensive entry status management
- Historical tracking of status changes

### Activity Tracking
- Track whether entries were active for each stage
- Supports DNF/DNS logic from EWRC imports

### Championship Flexibility
- Multiple championships per rally
- Coefficient-based point adjustments
- Class-specific standings

## Usage Notes

### Database Initialization
Run `StageTimeSchema.sql` to create the complete database structure.

### Schema Verification
Use `check_database_vs_schema.sql` to verify your database matches the expected structure.

### Backup Reference
All experimental and debug files are preserved in `sql_backup/` for historical reference.

## Recent Updates

- Consolidated all schema elements into single file
- Added activity tracking for EWRC imports
- Enhanced championship system with multi-rally support
- Added shakedown and itinerary management
- Improved status tracking with history
- Updated views to exclude retired entries from standings
