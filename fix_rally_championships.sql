-- Script to fix rally championship associations
-- Replace the values below with your actual rally and championship data

-- Example: Fix multiple Greek rallies to be in Greek National Championship instead of WRC

BEGIN;

-- Remove Greek rallies from WRC (if incorrectly associated)
DELETE FROM championship_events 
WHERE rally_id IN (
    SELECT r.id 
    FROM rallies r 
    WHERE r.country = 'Greece'
    AND r.start_date >= '2024-01-01'
) 
AND championship_id = 'wrc-2024';

-- Add Greek rallies to Greek National Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT 
    'greek-national-2024' as championship_id,
    r.id as rally_id,
    1.0 as coefficient
FROM rallies r 
WHERE r.country = 'Greece'
AND r.start_date >= '2024-01-01'
AND NOT EXISTS (
    SELECT 1 FROM championship_events ce 
    WHERE ce.rally_id = r.id 
    AND ce.championship_id = 'greek-national-2024'
);

-- Verify the changes
SELECT 
    r.name as rally_name,
    r.country,
    c.name as championship_name,
    ce.coefficient
FROM rallies r
JOIN championship_events ce ON r.id = ce.rally_id
JOIN championships c ON ce.championship_id = c.id
WHERE r.country = 'Greece'
AND r.start_date >= '2024-01-01'
ORDER BY r.start_date, c.name;

COMMIT;
