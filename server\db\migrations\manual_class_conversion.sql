-- Manual Class Conversion Helper
-- This script helps you manually convert your space-separated classes to comma-separated

-- Step 1: See all unique class combinations currently in your database
SELECT DISTINCT class, COUNT(*) as usage_count
FROM entries 
WHERE class IS NOT NULL AND class != ''
GROUP BY class
ORDER BY usage_count DESC, class;

-- Step 2: Create backup
CREATE TABLE IF NOT EXISTS entries_class_backup AS
SELECT id, class as original_class, NOW() as backup_date
FROM entries;

-- Step 3: Manual conversion examples
-- You'll need to run these one by one based on your actual data

-- Example conversions (update these based on your actual class combinations):

-- Convert "C6 F2 Under 30" to "C6, F2, Under 30"
UPDATE entries 
SET class = 'C6, F2, Under 30'
WHERE class = 'C6 F2 Under 30';

-- Convert "C6 F2 E" to "C6, F2, E"  
UPDATE entries 
SET class = 'C6, F2, E'
WHERE class = 'C6 F2 E';

-- Add more conversions as needed...
-- UPDATE entries SET class = 'new, comma, separated' WHERE class = 'old space separated';

-- Step 4: Verify conversions
SELECT 
  eb.original_class as before,
  e.class as after,
  COUNT(*) as count
FROM entries e
JOIN entries_class_backup eb ON e.id = eb.id
WHERE eb.original_class != e.class
GROUP BY eb.original_class, e.class
ORDER BY count DESC;

-- Step 5: Check for any remaining space-separated classes
SELECT DISTINCT class, COUNT(*) as count
FROM entries 
WHERE class IS NOT NULL 
  AND class != ''
  AND class NOT LIKE '%,%'  -- No commas
  AND class LIKE '% %'      -- Has spaces
GROUP BY class
ORDER BY count DESC;
