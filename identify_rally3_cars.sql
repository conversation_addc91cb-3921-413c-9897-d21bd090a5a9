-- Script to identify and fix Rally3 cars for Rally3 championship

-- First, let's see what cars exist in Rally Stereas Elladas 2025
SELECT 'All cars in Rally Stereas Elladas 2025:' as info;
SELECT DISTINCT
    e.car,
    e.class,
    COUNT(*) as count
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
GROUP BY e.car, e.class
ORDER BY e.car;

-- Look for potential Rally3 cars by car model
SELECT 'Potential Rally3 cars (by model):' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (
    e.car LIKE '%Rally3%' 
    OR e.car LIKE '%R3%'
    OR e.car LIKE '%Clio%'  -- Renault Clio Rally3
    OR e.car LIKE '%208%'   -- Peugeot 208 Rally3
    OR e.car LIKE '%Corsa%' -- Opel Corsa Rally3
    OR e.car LIKE '%Fiesta%' -- Ford Fiesta Rally3
)
ORDER BY e.number;

-- If no Rally3 cars found by model, let's check if any entries need Rally3 class assignment
SELECT 'Entries that might need Rally3 class assignment:' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class,
    'Potential Rally3 candidate' as note
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND e.class IS NULL  -- Entries without class assigned
ORDER BY e.number;

-- Commands to assign Rally3 class to specific cars (examples)
SELECT 'Example commands to assign Rally3 class:' as info;
SELECT 'UPDATE entries SET class = ''Rally3'' WHERE car LIKE ''%Clio Rally3%'';' as sql_command;
SELECT 'UPDATE entries SET class = ''Rally3'' WHERE car LIKE ''%208 Rally3%'';' as sql_command;
SELECT 'UPDATE entries SET class = ''Rally3'' WHERE car LIKE ''%Corsa Rally3%'';' as sql_command;

-- Or assign by specific entry numbers if you know which ones are Rally3
SELECT 'Or assign by entry number:' as info;
SELECT 'UPDATE entries SET class = ''Rally3'' WHERE number IN (''1'', ''2'', ''3'') AND rally_id = (SELECT id FROM rallies WHERE name LIKE ''%Stereas Elladas 2025%'');' as sql_command;

-- Check current Rally3 championship entries (should be 0 before fixing)
SELECT 'Current Rally3 championship entries (before fix):' as info;
SELECT COUNT(*) as rally3_entries
FROM championship_overall_classification
WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb'
AND rally_name LIKE '%Stereas Elladas%';
