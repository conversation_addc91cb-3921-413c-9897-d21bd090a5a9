#!/usr/bin/env node

// Test script to verify the EWRC import functionality
// This script tests the new features: stage distance, date, time, and championship linking

import fs from 'fs';
import path from 'path';

// Sample test data matching the new JSON structure
const testData = {
  "rally_info": {
    "name": "Test Rally 2025",
    "championship": [
      "Greece",
      "Historic (GR)",
      "Rally3 (GR)"
    ],
    "start_date": "24.5.2025",
    "end_date": "25.5.2025",
    "distance_meters": 89900,
    "total_entries": 3,
    "finished": 0
  },
  "results": [
    {
      "stage_number": 1,
      "stage_name": "Test Stage 1",
      "stage_distance_km": 22.79,
      "stage_date": "25.5.",
      "stage_start_time": "09:23",
      "country": "greece",
      "driver_codriver": "Test Driver - Test Codriver",
      "entry_number": "#1",
      "group": ["C1"],
      "car": "Test Car",
      "stage_time_ms": 993240,
      "overall_time_ms": 993240,
      "penalties_ms": 0,
      "super_rally": false,
      "active": true,
      "nominal_time": false,
      "comments": null
    },
    {
      "stage_number": 2,
      "stage_name": "Test Stage 2",
      "stage_distance_km": 15.5,
      "stage_date": "25.5.",
      "stage_start_time": "11:30",
      "country": "greece",
      "driver_codriver": "Test Driver - Test Codriver",
      "entry_number": "#1",
      "group": ["C1"],
      "car": "Test Car",
      "stage_time_ms": 654321,
      "overall_time_ms": 1647561,
      "penalties_ms": 0,
      "super_rally": false,
      "active": true,
      "nominal_time": false,
      "comments": null
    }
  ]
};

// Test the date parsing function (updated to match server logic)
function parseStageDateTime(stageDate, stageTime, rallyYear) {
  if (!stageDate || !stageTime) {
    return null;
  }

  try {
    // Parse date like "25.5." and combine with rally year
    // First split by dots, then clean up the parts
    const dateParts = stageDate.split('.').filter(part => part.length > 0);
    if (dateParts.length >= 2) {
      const day = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]);

      // Parse time like "09:23"
      const timeParts = stageTime.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = parseInt(timeParts[1]);

      // Create date object
      const stageDateTime = new Date(rallyYear, month - 1, day, hours, minutes);
      return stageDateTime.toISOString();
    }
  } catch (error) {
    console.warn(`Failed to parse stage date/time: ${stageDate} ${stageTime}`, error);
  }

  return null;
}

console.log('Testing EWRC Import Functionality');
console.log('==================================');

// Test 1: Date parsing
console.log('\n1. Testing date parsing:');
const testDate1 = parseStageDateTime("25.5.", "09:23", 2025);
console.log(`Input: "25.5.", "09:23", 2025`);
console.log(`Output: ${testDate1}`);
console.log(`Expected: 2025-05-25T07:23:00.000Z (UTC)`);

const testDate2 = parseStageDateTime("25.5.", "10:46", 2025);
console.log(`Input: "25.5.", "10:46", 2025`);
console.log(`Output: ${testDate2}`);
console.log(`Expected: 2025-05-25T08:46:00.000Z (UTC)`);

// Test 2: Stage data extraction
console.log('\n2. Testing stage data extraction:');
const stagesByNumber = new Map();
for (const result of testData.results) {
  if (!stagesByNumber.has(result.stage_number)) {
    stagesByNumber.set(result.stage_number, {
      number: result.stage_number,
      name: result.stage_name,
      distance_km: result.stage_distance_km || 10.0,
      date: result.stage_date,
      start_time: result.stage_start_time
    });
  }
}

console.log('Extracted stages:');
for (const stage of stagesByNumber.values()) {
  console.log(`  Stage ${stage.number}: ${stage.name} (${stage.distance_km}km) at ${stage.start_time} on ${stage.date}`);
}

// Test 3: Championship data
console.log('\n3. Testing championship data:');
console.log('Championships to link:');
testData.rally_info.championship.forEach(champ => {
  console.log(`  - ${champ}`);
});

// Save test data to file for manual testing
const testFilePath = 'test_rally_data.json';
fs.writeFileSync(testFilePath, JSON.stringify(testData, null, 2));
console.log(`\n4. Test data saved to: ${testFilePath}`);
console.log('You can use this file to test the import functionality in the admin panel.');

// Test 4: Check what happens with the regex
console.log('\n4. Testing regex replacement:');
const testString = "25.5.";
console.log(`Original: "${testString}"`);
console.log(`After replace(/\./g, ''): "${testString.replace(/\./g, '')}"`);
console.log(`After split('.'): [${testString.replace(/\./g, '').split('.')}]`);

// Test 5: Manual step-by-step parsing
console.log('\n5. Manual step-by-step parsing:');
function debugParseStageDateTime(stageDate, stageTime, rallyYear) {
  console.log(`Input: stageDate="${stageDate}", stageTime="${stageTime}", rallyYear=${rallyYear}`);

  if (!stageDate || !stageTime) {
    console.log('❌ Missing stage date or time');
    return null;
  }

  try {
    console.log(`Step 1: Original stageDate = "${stageDate}"`);

    // Parse date like "25.5." and combine with rally year
    // First split by dots, then clean up the parts
    const dateParts = stageDate.split('.').filter(part => part.length > 0);
    console.log(`Step 2: After split('.').filter() = [${dateParts.join(', ')}]`);
    console.log(`Step 3: dateParts.length = ${dateParts.length}`);

    if (dateParts.length >= 2) {
      const day = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]);
      console.log(`Step 4: Parsed day=${day}, month=${month}`);

      // Parse time like "09:23"
      const timeParts = stageTime.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = parseInt(timeParts[1]);
      console.log(`Step 5: Parsed hours=${hours}, minutes=${minutes}`);

      // Create date object
      const stageDateTime = new Date(rallyYear, month - 1, day, hours, minutes);
      const isoString = stageDateTime.toISOString();
      console.log(`Step 6: Created Date object = ${stageDateTime}`);
      console.log(`Step 7: ISO string = ${isoString}`);

      return isoString;
    } else {
      console.log('❌ Not enough date parts');
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

debugParseStageDateTime("25.5.", "09:23", 2025);
debugParseStageDateTime("25.5.", "10:46", 2025);

console.log('\n✅ All tests completed successfully!');
