'use client';

import Link from 'next/link';
import { 
  Flag, 
  UserPlus, 
  ClipboardList, 
  Timer, 
  Trophy, 
  AlertTriangle, 
  FileText 
} from 'lucide-react';

export default function DashboardPage() {
  // Quick actions for admin dashboard
  const quickActions = [
    {
      href: '/admin/rallies/add',
      icon: <Flag className="mr-2 h-5 w-5" />, 
      label: 'Add Rally', 
      desc: 'Create a new rally', 
      manageHref: '/admin/rallies', 
      manageLabel: 'Manage Rallies',
    },
    {
      href: '/admin/persons/add',
      icon: <UserPlus className="mr-2 h-5 w-5" />, 
      label: 'Add Person', 
      desc: 'Register a new person', 
      manageHref: '/admin/persons', 
      manageLabel: 'Manage Persons',
    },
    {
      href: '/admin/entries/add',
      icon: <ClipboardList className="mr-2 h-5 w-5" />, 
      label: 'Add Entry', 
      desc: 'Register a rally entry', 
      manageHref: '/admin/entries', 
      manageLabel: 'Manage Entries',
    },
    {
      href: '/admin/stages/add',
      icon: <Timer className="mr-2 h-5 w-5" />, 
      label: 'Add Stage', 
      desc: 'Add a stage to a rally', 
      manageHref: '/admin/stages', 
      manageLabel: 'Manage Stages',
    },
    {
      href: '/admin/stageResults/add',
      icon: <Trophy className="mr-2 h-5 w-5" />, 
      label: 'Add Stage Result', 
      desc: 'Enter a stage result', 
      manageHref: '/admin/stageResults', 
      manageLabel: 'Manage Stage Results',
    },
    {
      href: '/admin/penalties/add',
      icon: <AlertTriangle className="mr-2 h-5 w-5" />, 
      label: 'Add Penalty', 
      desc: 'Record a penalty', 
      manageHref: '/admin/penalties', 
      manageLabel: 'Manage Penalties',
    },
    {
      href: '/admin/news/add',
      icon: <FileText className="mr-2 h-5 w-5" />, 
      label: 'Add News', 
      desc: 'Publish a news article', 
      manageHref: '/admin/news', 
      manageLabel: 'Manage News',
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quickActions.map((action, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-2 flex items-center">
              {action.icon}
              {action.label}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{action.desc}</p>
            <div className="flex space-x-3">
              <Link
                href={action.href}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                {action.label}
              </Link>
              <Link
                href={action.manageHref}
                className="bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                {action.manageLabel}
              </Link>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
