-- Fix championship views to filter entries by class
-- This ensures entries only appear in appropriate championships

-- First, let's create a championship_classes table to define which classes belong to which championships
CREATE TABLE IF NOT EXISTS championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    class_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(championship_id, class_name)
);

-- Example: Insert class mappings for different championship types
-- You'll need to adjust these based on your actual championship names and classes

-- WRC Championship - only WRC class entries
INSERT INTO championship_classes (championship_id, class_name) VALUES
('wrc-2025', 'WRC'),
('wrc-2025', 'Rally1') ON CONFLICT DO NOTHING;

-- WRC2 Championship - R5/Rally2 classes
INSERT INTO championship_classes (championship_id, class_name) VALUES
('wrc2-2025', 'WRC2'),
('wrc2-2025', 'R5'),
('wrc2-2025', 'Rally2') ON CONFLICT DO NOTHING;

-- Greek National Championship - all classes
INSERT INTO championship_classes (championship_id, class_name) VALUES
('greek-national-2025', 'WRC'),
('greek-national-2025', 'WRC2'),
('greek-national-2025', 'R5'),
('greek-national-2025', 'Rally2'),
('greek-national-2025', 'N4'),
('greek-national-2025', 'R2'),
('greek-national-2025', 'Historic') ON CONFLICT DO NOTHING;

-- Historic Championship - only historic classes
INSERT INTO championship_classes (championship_id, class_name) VALUES
('historic-2025', 'Historic'),
('historic-2025', 'H1'),
('historic-2025', 'H2'),
('historic-2025', 'H3') ON CONFLICT DO NOTHING;

-- Now update the championship_overall_classification view to filter by class
DROP VIEW IF EXISTS championship_overall_classification;

CREATE VIEW championship_overall_classification AS
SELECT DISTINCT
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    -- Calculate championship points with coefficient
    CASE 
        WHEN oc.position = 1 THEN 25 * ce.coefficient
        WHEN oc.position = 2 THEN 18 * ce.coefficient
        WHEN oc.position = 3 THEN 15 * ce.coefficient
        WHEN oc.position = 4 THEN 12 * ce.coefficient
        WHEN oc.position = 5 THEN 10 * ce.coefficient
        WHEN oc.position = 6 THEN 8 * ce.coefficient
        WHEN oc.position = 7 THEN 6 * ce.coefficient
        WHEN oc.position = 8 THEN 4 * ce.coefficient
        WHEN oc.position = 9 THEN 2 * ce.coefficient
        WHEN oc.position = 10 THEN 1 * ce.coefficient
        ELSE 0
    END as championship_points
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
-- KEY CHANGE: Filter by championship classes
JOIN championship_classes cc ON ce.championship_id = cc.championship_id
WHERE e.status IN ('finished', 'retired', 'dnf')
-- Check if entry class matches any of the championship's allowed classes
AND (
    e.class LIKE '%' || cc.class_name || '%' 
    OR cc.class_name = 'ALL'  -- Special case for championships that accept all classes
)
ORDER BY ce.championship_id, r.start_date, oc.position;
