-- Simple Time Debug
-- Let's see where the championship times are coming from

-- 1. Check <PERSON><PERSON><PERSON><PERSON><PERSON>'s stage times
SELECT 'Papadimitriou stage times:' as debug;
SELECT 
    pd.first_name || ' ' || pd.last_name as driver,
    e.class,
    s.name as stage_name,
    res.time as stage_time,
    COALESCE(p.time, 0) as penalty_time,
    res.time + COALESCE(p.time, 0) as total_stage_time
FROM results res
JOIN entries e ON res.entry_id = e.id
JOIN rallies r ON e.rally_id = r.id
JOIN persons pd ON e.driver_id = pd.id
JOIN stages s ON res.stage_id = s.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND pd.last_name LIKE '%Papadimitriou%'
ORDER BY res.time;

-- 2. Calculate Papa<PERSON><PERSON><PERSON><PERSON>'s total time manually
SELECT 'Papadimitriou total time calculation:' as debug;
SELECT 
    pd.first_name || ' ' || pd.last_name as driver,
    e.class,
    COUNT(res.time) as stages_completed,
    SUM(res.time) as total_stage_times,
    SUM(COALESCE(p.time, 0)) as total_penalties,
    SUM(res.time + COALESCE(p.time, 0)) as total_rally_time
FROM results res
JOIN entries e ON res.entry_id = e.id
JOIN rallies r ON e.rally_id = r.id
JOIN persons pd ON e.driver_id = pd.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND pd.last_name LIKE '%Papadimitriou%'
GROUP BY e.id, pd.first_name, pd.last_name, e.class;

-- 3. Compare with overall_classification view
SELECT 'Papadimitriou in overall_classification:' as debug;
SELECT 
    oc.position as overall_position,
    oc.driver,
    oc.total_time as overall_total_time,
    e.class
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND oc.driver LIKE '%Papadimitriou%';

-- 4. Compare with championship view
SELECT 'Papadimitriou in championship view:' as debug;
SELECT 
    championship_position,
    driver,
    total_time as championship_total_time,
    class
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
AND driver LIKE '%Papadimitriou%';

-- 5. Show top 5 from overall_classification
SELECT 'Top 5 from overall_classification:' as debug;
SELECT 
    oc.position,
    oc.driver,
    e.class,
    oc.total_time
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
ORDER BY oc.position
LIMIT 5;

-- 6. Show top 5 from championship view for Greece
SELECT 'Top 5 from championship view (Greece):' as debug;
SELECT 
    championship_position,
    driver,
    class,
    total_time
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_position
LIMIT 5;

-- 7. Check if times match between views
SELECT 'Time comparison between views:' as debug;
SELECT 
    oc.driver,
    oc.position as overall_pos,
    oc.total_time as overall_time,
    coc.championship_position as champ_pos,
    coc.total_time as champ_time,
    CASE 
        WHEN oc.total_time = coc.total_time THEN 'TIMES MATCH'
        ELSE 'TIMES DIFFERENT'
    END as time_status
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
JOIN championship_overall_classification coc ON coc.driver = oc.driver 
    AND coc.rally_name = r.name
WHERE r.name = 'Rally Stereas Elladas 2025'
AND coc.championship_name = 'Greece'
ORDER BY oc.position
LIMIT 10;
