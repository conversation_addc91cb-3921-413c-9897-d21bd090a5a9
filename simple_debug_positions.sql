-- Simple Debug for Championship Positions
-- Let's see what's wrong with the ranking

-- 1. Check current Greece Championship results ordered by time
SELECT 'Greece Championship - Ordered by Total Time (fastest first):' as debug;
SELECT 
    championship_position,
    driver,
    class,
    total_time,
    RANK() OVER (ORDER BY total_time) as correct_rank_by_time
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY total_time
LIMIT 15;

-- 2. Check for data issues (simplified)
SELECT 'Checking for NULL or zero times:' as debug;
SELECT 
    driver,
    class,
    total_time,
    championship_position
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
AND (total_time IS NULL OR total_time <= 0)
ORDER BY total_time;

-- 3. Show the mismatch between current positions and correct positions
SELECT 'Position Mismatch Analysis:' as debug;
WITH correct_positions AS (
    SELECT 
        driver,
        class,
        total_time,
        championship_position as current_position,
        RANK() OVER (ORDER BY total_time) as correct_position
    FROM championship_overall_classification
    WHERE championship_name = 'Greece' 
    AND rally_name = 'Rally Stereas Elladas 2025'
)
SELECT 
    current_position,
    correct_position,
    driver,
    class,
    total_time,
    CASE 
        WHEN current_position != correct_position THEN 'MISMATCH'
        ELSE 'OK'
    END as status
FROM correct_positions
ORDER BY total_time
LIMIT 15;

-- 4. Check what Papadimitriou Ioannis should be
SELECT 'Papadimitriou Ioannis analysis:' as debug;
SELECT 
    driver,
    class,
    total_time,
    championship_position,
    RANK() OVER (ORDER BY total_time) as should_be_position
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
AND driver LIKE '%Papadimitriou%'
ORDER BY total_time;

-- 5. Show top 5 fastest times vs current positions
SELECT 'Top 5 fastest vs current positions:' as debug;
SELECT 
    RANK() OVER (ORDER BY total_time) as rank_by_time,
    championship_position as current_position,
    driver,
    class,
    total_time
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY total_time
LIMIT 5;
