'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';

interface Championship {
  id: string;
  name: string;
  year: number;
}

interface RallyFormProps {
  initialData?: any;
  championships: Championship[];
}

export default function RallyForm({ initialData, championships }: RallyFormProps) {
  const [form, setForm] = useState({
    name: '',
    country: '',
    start_date: '',
    end_date: '',
    surface: '',
    status: '',
    championship_id: '',
    logo_url: '',
    banner_url: ''
  });
  const [flagCode, setFlagCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (initialData) {
      setForm({
        name: initialData.name || '',
        country: initialData.country || '',
        start_date: initialData.start_date ? initialData.start_date.slice(0, 10) : '',
        end_date: initialData.end_date ? initialData.end_date.slice(0, 10) : '',
        surface: initialData.surface || '',
        status: initialData.status || '',
        championship_id: initialData.championship_id || '',
        logo_url: initialData.logo_url || '',
        banner_url: initialData.banner_url || ''
      });
    }
  }, [initialData]);

  // Update flag code when country changes
  useEffect(() => {
    setFlagCode(getCountryCode(form.country));
  }, [form.country]);

  // Helper function to get country code from country name
  const getCountryCode = (country: string): string => {
    // This is a placeholder - you would implement your actual country code logic here
    return country.toLowerCase().substring(0, 2);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      if (initialData) {
        // Update existing rally
        const { error } = await supabase
          .from('rallies')
          .update(form)
          .eq('id', initialData.id);
        
        if (error) throw error;
      } else {
        // Create new rally
        const { error } = await supabase
          .from('rallies')
          .insert([form]);
        
        if (error) throw error;
      }
      
      router.push('/admin/rallies');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const surfaceOptions = [
    { value: 'Tarmac', label: 'Tarmac' },
    { value: 'Gravel', label: 'Gravel' },
    { value: 'Snow', label: 'Snow' },
    { value: 'Mixed', label: 'Mixed' },
  ];

  const statusOptions = [
    { value: 'Upcoming', label: 'Upcoming' },
    { value: 'Ongoing', label: 'Ongoing' },
    { value: 'Completed', label: 'Completed' },
    { value: 'Cancelled', label: 'Cancelled' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">{error}</div>}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Rally Name
            </label>
            <input
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              placeholder="Rally Name"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Country
            </label>
            <div className="relative">
              <input
                name="country"
                value={form.country}
                onChange={handleChange}
                required
                placeholder="Country"
                className={`w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white ${flagCode ? 'pl-10' : ''}`}
              />
              {flagCode && (
                <span
                  className={`fi fi-${flagCode} absolute left-3 top-1/2 transform -translate-y-1/2`}
                  style={{ width: '20px', height: '15px' }}
                ></span>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              name="start_date"
              value={form.start_date}
              onChange={handleChange}
              type="date"
              required
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              name="end_date"
              value={form.end_date}
              onChange={handleChange}
              type="date"
              required
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Surface
            </label>
            <select
              name="surface"
              value={form.surface}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Surface</option>
              {surfaceOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              name="status"
              value={form.status}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Status</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Championship
            </label>
            <select
              name="championship_id"
              value={form.championship_id}
              onChange={handleChange}
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            >
              <option value="">None</option>
              {championships.map(championship => (
                <option key={championship.id} value={championship.id}>
                  {championship.name} {championship.year}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Logo URL
            </label>
            <input
              name="logo_url"
              value={form.logo_url}
              onChange={handleChange}
              placeholder="Logo URL"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Banner URL
            </label>
            <input
              name="banner_url"
              value={form.banner_url}
              onChange={handleChange}
              placeholder="Banner URL"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={() => router.push('/admin/rallies')}
            className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Saving...' : initialData ? 'Save Changes' : 'Add Rally'}
          </button>
        </div>
      </form>
    </div>
  );
}
