-- Setup Universal Championships for All Rallies
-- This will work with any rally and any class structure

-- First, let's see what we're working with
SELECT 'Current rallies in database:' as info;
SELECT
    r.id,
    r.name,
    r.country,
    r.start_date,
    COUNT(e.id) as total_entries,
    COUNT(DISTINCT e.class) as unique_classes
FROM rallies r
LEFT JOIN entries e ON r.id = e.rally_id
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC
LIMIT 10;

SELECT 'All unique classes across all rallies:' as info;
SELECT
    e.class,
    COUNT(*) as usage_count,
    COUNT(DISTINCT r.country) as countries_used,
    STRING_AGG(DISTINCT r.country, ', ' ORDER BY r.country) as countries
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE e.class IS NOT NULL AND e.class != ''
GROUP BY e.class
ORDER BY usage_count DESC;

-- Clear existing championship class mappings
DELETE FROM championship_classes;

-- Setup Greek Championship System (works for all Greek rallies)
-- 1. Greece Championship - All C-classes (C1, C2, C3, C4, C5, C6)
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C1', 'contains', 'Greek C1 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C2', 'contains', 'Greek C2 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C3', 'contains', 'Greek C3 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C4', 'contains', 'Greek C4 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C5', 'contains', 'Greek C5 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C6', 'contains', 'Greek C6 class');

-- 2. Historic Championship - Numeric classes (1, 2, 3, 4) - exact match to avoid C1, C2, etc.
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '^1$', 'regex', 'Historic class 1'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '^2$', 'regex', 'Historic class 2'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '^3$', 'regex', 'Historic class 3'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '^4$', 'regex', 'Historic class 4');

-- Alternative for Historic (if regex not supported, use exact match)
-- INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
-- ('cc1e9147-fdb3-4483-aad3-9e27795eff17', '1', 'exact', 'Historic class 1'),
-- ('cc1e9147-fdb3-4483-aad3-9e27795eff17', '2', 'exact', 'Historic class 2'),
-- ('cc1e9147-fdb3-4483-aad3-9e27795eff17', '3', 'exact', 'Historic class 3'),
-- ('cc1e9147-fdb3-4483-aad3-9e27795eff17', '4', 'exact', 'Historic class 4');

-- 3. Historic Gravel Cup - Same as Historic
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '^1$', 'regex', 'Historic Gravel class 1'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '^2$', 'regex', 'Historic Gravel class 2'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '^3$', 'regex', 'Historic Gravel class 3'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '^4$', 'regex', 'Historic Gravel class 4');

-- 4. Rally3 Championship - Rally3 cars (can be identified by class or car model)
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'Rally3', 'contains', 'Rally3 cars'),
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'R3', 'contains', 'R3 class cars');

-- Add more universal class patterns that might appear in other rallies
-- WRC classes (if you have international rallies)
-- INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
-- ('wrc-championship-id', 'WRC', 'contains', 'WRC cars'),
-- ('wrc-championship-id', 'Rally1', 'contains', 'Rally1 cars');

-- R5/Rally2 classes (common in many countries)
-- INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
-- ('r5-championship-id', 'R5', 'contains', 'R5 cars'),
-- ('r5-championship-id', 'Rally2', 'contains', 'Rally2 cars');

-- Setup championship-specific points (optional - uses default if not specified)
-- Example: Rally3 championship with different points
-- INSERT INTO championship_points_system (championship_id, position, points) VALUES
-- ('895f159e-f147-439b-b5ab-04972033a7bb', 1, 20),
-- ('895f159e-f147-439b-b5ab-04972033a7bb', 2, 15),
-- ('895f159e-f147-439b-b5ab-04972033a7bb', 3, 12),
-- ('895f159e-f147-439b-b5ab-04972033a7bb', 4, 10),
-- ('895f159e-f147-439b-b5ab-04972033a7bb', 5, 8);

-- Verify the setup
SELECT 'Championship class mappings:' as info;
SELECT
    c.name as championship,
    cc.class_pattern,
    cc.match_type,
    cc.description
FROM championship_classes cc
JOIN championships c ON cc.championship_id = c.id
ORDER BY c.name, cc.class_pattern;

-- Test with actual data
SELECT 'Championship entry distribution across all rallies:' as info;
SELECT
    cr.championship_name,
    COUNT(*) as total_entries,
    COUNT(DISTINCT cr.rally_name) as rallies_included,
    COUNT(DISTINCT cr.driver) as unique_drivers,
    STRING_AGG(DISTINCT cr.class, ', ' ORDER BY cr.class) as classes_included
FROM championship_results cr
GROUP BY cr.championship_id, cr.championship_name
ORDER BY total_entries DESC;

-- Show rally distribution per championship
SELECT 'Rallies per championship:' as info;
SELECT
    c.name as championship,
    COUNT(DISTINCT r.id) as total_rallies,
    STRING_AGG(DISTINCT r.name, ', ' ORDER BY r.start_date) as rally_names
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name
ORDER BY total_rallies DESC;

-- Show current championship standings (top 5 per championship)
SELECT 'Current championship leaders:' as info;
SELECT
    championship_name,
    championship_position,
    driver,
    total_points,
    rallies_participated,
    wins,
    podiums
FROM championship_standings
WHERE championship_position <= 5
ORDER BY championship_name, championship_position;
