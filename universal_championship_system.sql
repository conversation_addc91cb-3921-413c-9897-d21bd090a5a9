-- Universal Championship System
-- Handles all rallies, all classes, all championships - fully scalable

-- 1. Championship Classes Table - Maps which classes belong to which championships
CREATE TABLE IF NOT EXISTS championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id UUID NOT NULL,
    class_pattern VARCHAR(100) NOT NULL,
    match_type VARCHAR(20) DEFAULT 'contains' CHECK (match_type IN ('exact', 'contains', 'starts_with', 'ends_with', 'regex')),
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE (championship_id, class_pattern, match_type)
);

-- Create indexes for championship_classes
CREATE INDEX IF NOT EXISTS idx_championship_classes_championship ON championship_classes(championship_id);
CREATE INDEX IF NOT EXISTS idx_championship_classes_pattern ON championship_classes(class_pattern);

-- 2. Championship Points System Table - Flexible points per championship
CREATE TABLE IF NOT EXISTS championship_points_system (
    id SERIAL PRIMARY KEY,
    championship_id UUID NOT NULL,
    position INTEGER NOT NULL,
    points DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(championship_id, position)
);

-- Create index for championship_points_system
CREATE INDEX IF NOT EXISTS idx_points_system_championship ON championship_points_system(championship_id);

-- 3. Championship Settings Table - Additional championship configuration
CREATE TABLE IF NOT EXISTS championship_settings (
    id SERIAL PRIMARY KEY,
    championship_id UUID NOT NULL UNIQUE,
    min_rallies_for_points INTEGER DEFAULT 1,
    max_rallies_counted INTEGER DEFAULT NULL, -- NULL = count all rallies
    drop_worst_results INTEGER DEFAULT 0,
    bonus_points_leader DECIMAL(5,2) DEFAULT 0,
    bonus_points_fastest_stage DECIMAL(5,2) DEFAULT 0,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE
);

-- 4. Insert default points system (F1-style) for existing championships
-- We'll insert default points for each existing championship
INSERT INTO championship_points_system (championship_id, position, points)
SELECT c.id, pos.position, pos.points
FROM championships c
CROSS JOIN (
    VALUES (1, 25), (2, 18), (3, 15), (4, 12), (5, 10),
           (6, 8), (7, 6), (8, 4), (9, 2), (10, 1)
) AS pos(position, points)
WHERE NOT EXISTS (
    SELECT 1 FROM championship_points_system cps
    WHERE cps.championship_id = c.id AND cps.position = pos.position
);

-- 5. Insert default championship settings
INSERT INTO championship_settings (championship_id)
SELECT DISTINCT id FROM championships
WHERE id NOT IN (SELECT championship_id FROM championship_settings)
ON CONFLICT (championship_id) DO NOTHING;

-- 6. Universal Championship Results View
DROP VIEW IF EXISTS championship_results;
CREATE VIEW championship_results AS
WITH championship_entries AS (
    -- First, get all eligible entries for each championship
    SELECT
        ce.championship_id,
        c.name as championship_name,
        c.year as championship_year,
        r.id as rally_id,
        r.name as rally_name,
        r.country as rally_country,
        r.start_date as rally_date,
        e.id as entry_id,
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        pd.nationality as driver_nationality,
        CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
        pc.nationality as codriver_nationality,
        e.car,
        e.class,
        e.status,
        oc.position,
        oc.total_time,
        oc.time_diff,
        ce.coefficient as rally_coefficient,
        -- Calculate championship position within this rally for this championship
        ROW_NUMBER() OVER (
            PARTITION BY ce.championship_id, r.id
            ORDER BY oc.position NULLS LAST
        ) as championship_position
    FROM championship_events ce
    JOIN championships c ON ce.championship_id = c.id
    JOIN rallies r ON ce.rally_id = r.id
    JOIN entries e ON r.id = e.rally_id
    JOIN persons pd ON e.driver_id = pd.id
    JOIN persons pc ON e.codriver_id = pc.id
    JOIN overall_classification oc ON e.id = oc.entry_id
    WHERE e.status IN ('finished', 'retired', 'dnf')
    AND EXISTS (
        SELECT 1 FROM championship_classes cc
        WHERE cc.championship_id = ce.championship_id
        AND cc.active = true
        AND (
            (cc.match_type = 'exact' AND e.class = cc.class_pattern) OR
            (cc.match_type = 'contains' AND e.class LIKE '%' || cc.class_pattern || '%') OR
            (cc.match_type = 'starts_with' AND e.class LIKE cc.class_pattern || '%') OR
            (cc.match_type = 'ends_with' AND e.class LIKE '%' || cc.class_pattern) OR
            (cc.match_type = 'regex' AND e.class ~ cc.class_pattern)
        )
    )
)
SELECT
    ce.*,
    -- Get points (championship-specific or calculate default F1 points)
    COALESCE(
        cps.points * COALESCE(ce.rally_coefficient, 1.0),
        CASE
            WHEN ce.championship_position = 1 THEN 25
            WHEN ce.championship_position = 2 THEN 18
            WHEN ce.championship_position = 3 THEN 15
            WHEN ce.championship_position = 4 THEN 12
            WHEN ce.championship_position = 5 THEN 10
            WHEN ce.championship_position = 6 THEN 8
            WHEN ce.championship_position = 7 THEN 6
            WHEN ce.championship_position = 8 THEN 4
            WHEN ce.championship_position = 9 THEN 2
            WHEN ce.championship_position = 10 THEN 1
            ELSE 0
        END * COALESCE(ce.rally_coefficient, 1.0)
    ) as points
FROM championship_entries ce
LEFT JOIN championship_points_system cps ON ce.championship_id = cps.championship_id
    AND ce.championship_position = cps.position
ORDER BY ce.championship_id, ce.rally_date, ce.championship_position;

-- 7. Championship Standings View
DROP VIEW IF EXISTS championship_standings;
CREATE VIEW championship_standings AS
SELECT
    cr.championship_id,
    cr.championship_name,
    cr.championship_year,
    cr.driver,
    cr.driver_nationality,
    COUNT(*) as rallies_participated,
    SUM(cr.points) as total_points,
    AVG(cr.points) as average_points,
    MAX(cr.points) as best_result_points,
    MIN(cr.championship_position) as best_position,
    COUNT(CASE WHEN cr.championship_position = 1 THEN 1 END) as wins,
    COUNT(CASE WHEN cr.championship_position <= 3 THEN 1 END) as podiums,
    COUNT(CASE WHEN cr.championship_position <= 10 THEN 1 END) as points_finishes,
    ROW_NUMBER() OVER (
        PARTITION BY cr.championship_id
        ORDER BY SUM(cr.points) DESC, COUNT(CASE WHEN cr.championship_position = 1 THEN 1 END) DESC
    ) as championship_position
FROM championship_results cr
GROUP BY cr.championship_id, cr.championship_name, cr.championship_year, cr.driver, cr.driver_nationality
ORDER BY cr.championship_id, championship_position;

-- 8. Championship Class Standings View (for class-specific standings within championships)
DROP VIEW IF EXISTS championship_class_standings;
CREATE VIEW championship_class_standings AS
SELECT
    cr.championship_id,
    cr.championship_name,
    cr.class,
    cr.driver,
    cr.driver_nationality,
    COUNT(*) as rallies_participated,
    SUM(cr.points) as total_points,
    ROW_NUMBER() OVER (
        PARTITION BY cr.championship_id, cr.class
        ORDER BY SUM(cr.points) DESC
    ) as class_position
FROM championship_results cr
GROUP BY cr.championship_id, cr.championship_name, cr.class, cr.driver, cr.driver_nationality
ORDER BY cr.championship_id, cr.class, class_position;

-- 9. Championship Calendar View
DROP VIEW IF EXISTS championship_calendar;
CREATE VIEW championship_calendar AS
SELECT
    c.id as championship_id,
    c.name as championship_name,
    c.year as championship_year,
    COUNT(ce.rally_id) as total_rallies,
    COUNT(CASE WHEN r.start_date < CURRENT_DATE THEN 1 END) as completed_rallies,
    COUNT(CASE WHEN r.start_date >= CURRENT_DATE THEN 1 END) as upcoming_rallies,
    MIN(r.start_date) as season_start,
    MAX(r.start_date) as season_end,
    STRING_AGG(
        r.name || ' (' || TO_CHAR(r.start_date, 'Mon DD') || ')',
        ', ' ORDER BY r.start_date
    ) as rally_list
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name, c.year
ORDER BY c.year DESC, c.name;
