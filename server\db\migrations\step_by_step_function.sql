-- Step-by-step function creation to avoid syntax issues

-- Step 1: Drop existing function and trigger if they exist
DROP TRIGGER IF EXISTS trigger_track_entry_status_change ON entries;
DROP FUNCTION IF EXISTS track_entry_status_change();

-- Step 2: Create the function with explicit syntax
CREATE FUNCTION track_entry_status_change()
RETURNS TRIGGER 
AS '
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO entry_status_history (
      entry_id, 
      rally_id, 
      old_status, 
      new_status,
      reason
    ) VALUES (
      NEW.id,
      NEW.rally_id,
      OLD.status,
      NEW.status,
      CASE 
        WHEN NEW.status IN (''retired'', ''dns'', ''dnf'', ''dsq'') THEN ''Entry marked as '' || NEW.status
        ELSE ''Status updated to '' || NEW.status
      END
    );
  END IF;
  
  RETURN NEW;
END;
' LANGUAGE plpgsql;

-- Step 3: Create the trigger
CREATE TRIGGER trigger_track_entry_status_change
  AFTER UPDATE ON entries
  FOR EACH ROW
  EXECUTE FUNCTION track_entry_status_change();
