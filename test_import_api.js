import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

async function testImportAPI() {
  try {
    console.log('=== Testing EWRC Import API ===');
    
    // Check if JSON file exists
    const jsonFile = './ewrc_complete_results_94918-rally-stereas-elladas-2025.json';
    if (!fs.existsSync(jsonFile)) {
      console.error('JSON file not found:', jsonFile);
      process.exit(1);
    }
    
    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(jsonFile));
    
    console.log('Uploading file to import API...');
    
    // Make request to import API
    const response = await fetch('http://localhost:5174/api/rallies/import-ewrc', {
      method: 'POST',
      body: formData,
      headers: {
        // Note: In a real scenario, you'd need authentication headers
        // For testing, we'll assume the admin middleware is bypassed or we have valid credentials
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Import successful!');
      console.log('Results:', JSON.stringify(result, null, 2));
    } else {
      console.log('❌ Import failed!');
      console.log('Error:', JSON.stringify(result, null, 2));
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error testing import API:', error);
    process.exit(1);
  }
}

testImportAPI();
