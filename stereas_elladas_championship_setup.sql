-- Championship class setup for Rally Stereas Elladas 2025
-- Based on your actual class data

-- First, let's see your current championships for this rally
SELECT 'Current Championships for Rally Stereas Elladas 2025:' as info;
SELECT 
    c.id,
    c.name as championship_name,
    ce.coefficient
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
WHERE r.name LIKE '%Stereas Elladas 2025%'
ORDER BY c.name;

-- Create championship_classes table if it doesn't exist
CREATE TABLE IF NOT EXISTS championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    class_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(championship_id, class_name)
);

-- Clear existing class mappings for this rally's championships
DELETE FROM championship_classes 
WHERE championship_id IN (
    SELECT ce.championship_id
    FROM championship_events ce
    JOIN rallies r ON ce.rally_id = r.id
    WHERE r.name LIKE '%Stereas Elladas 2025%'
);

-- Based on your class data, here's a suggested championship breakdown:
-- You'll need to replace 'championship-id-1' etc. with your actual championship IDs

-- Championship 1: Top Classes (C1, C2, 2, 3) - Modern/Fast cars
INSERT INTO championship_classes (championship_id, class_name) VALUES
('championship-id-1', 'C1'),
('championship-id-1', 'C2'),
('championship-id-1', '2'),
('championship-id-1', '3') ON CONFLICT DO NOTHING;

-- Championship 2: Middle Classes (C3, C4, 4) - Mid-level cars  
INSERT INTO championship_classes (championship_id, class_name) VALUES
('championship-id-2', 'C3'),
('championship-id-2', 'C4'),
('championship-id-2', '4') ON CONFLICT DO NOTHING;

-- Championship 3: Lower Classes (C5, C6) - Entry level cars
INSERT INTO championship_classes (championship_id, class_name) VALUES
('championship-id-3', 'C5'),
('championship-id-3', 'C6') ON CONFLICT DO NOTHING;

-- Championship 4: Special/Formula Classes (F2 variants) - Special cars
INSERT INTO championship_classes (championship_id, class_name) VALUES
('championship-id-4', 'F2'),
('championship-id-4', 'F2 E') ON CONFLICT DO NOTHING;

-- Alternative: If you want one championship to include all classes
-- INSERT INTO championship_classes (championship_id, class_name) VALUES
-- ('greek-national-championship-id', 'ALL') ON CONFLICT DO NOTHING;

-- Update the championship_overall_classification view to handle your class structure
DROP VIEW IF EXISTS championship_overall_classification;

CREATE VIEW championship_overall_classification AS
SELECT DISTINCT
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    -- Calculate championship points with coefficient
    CASE 
        WHEN oc.position = 1 THEN 25 * ce.coefficient
        WHEN oc.position = 2 THEN 18 * ce.coefficient
        WHEN oc.position = 3 THEN 15 * ce.coefficient
        WHEN oc.position = 4 THEN 12 * ce.coefficient
        WHEN oc.position = 5 THEN 10 * ce.coefficient
        WHEN oc.position = 6 THEN 8 * ce.coefficient
        WHEN oc.position = 7 THEN 6 * ce.coefficient
        WHEN oc.position = 8 THEN 4 * ce.coefficient
        WHEN oc.position = 9 THEN 2 * ce.coefficient
        WHEN oc.position = 10 THEN 1 * ce.coefficient
        ELSE 0
    END as championship_points
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
WHERE e.status IN ('finished', 'retired', 'dnf')
AND (
    -- Check if championship accepts all classes
    EXISTS (
        SELECT 1 FROM championship_classes cc 
        WHERE cc.championship_id = ce.championship_id 
        AND cc.class_name = 'ALL'
    )
    OR
    -- Check if entry class matches any championship class
    EXISTS (
        SELECT 1 FROM championship_classes cc 
        WHERE cc.championship_id = ce.championship_id 
        AND (
            e.class = cc.class_name 
            OR e.class LIKE cc.class_name || ',%'
            OR e.class LIKE '%,' || cc.class_name || ',%'
            OR e.class LIKE '%,' || cc.class_name
        )
    )
)
ORDER BY ce.championship_id, r.start_date, oc.position;

-- Show the results
SELECT 'Championship breakdown after class filtering:' as info;
SELECT 
    championship_name,
    class,
    COUNT(*) as entries,
    STRING_AGG(driver, ', ') as drivers
FROM championship_overall_classification coc
WHERE rally_name LIKE '%Stereas Elladas 2025%'
GROUP BY championship_id, championship_name, class
ORDER BY championship_name, entries DESC;
