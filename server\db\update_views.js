import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pool from '../config/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function updateViews() {
  try {
    console.log('Starting database view updates...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'update_views.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await pool.query(sql);
    
    console.log('Database views updated successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error updating database views:', error);
    process.exit(1);
  }
}

updateViews();
