-- Migration: Remove championship_id from rallies table and migrate data to championship_events
-- This migration enables multiple championships per rally

-- First, migrate existing data from rallies.championship_id to championship_events table
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT championship_id, id, 1.0
FROM rallies 
WHERE championship_id IS NOT NULL
ON CONFLICT DO NOTHING;

-- Remove the foreign key constraint and index
DROP INDEX IF EXISTS idx_rallies_championship_id;

-- Remove the championship_id column from rallies table
ALTER TABLE rallies DROP COLUMN IF EXISTS championship_id;

-- Add a unique constraint to prevent duplicate championship-rally combinations
ALTER TABLE championship_events 
ADD CONSTRAINT unique_championship_rally 
UNIQUE (championship_id, rally_id);

-- Update the championship_standings view to ensure it works correctly
DROP VIEW IF EXISTS championship_standings;

CREATE VIEW championship_standings AS
SELECT
  ce.championship_id,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(COALESCE(ps.points, 0)) AS total_points,
  RANK() OVER (PARTITION BY ce.championship_id ORDER BY SUM(COALESCE(ps.points, 0)) DESC) AS position
FROM championship_events ce
JOIN rallies r ON r.id = ce.rally_id
JOIN power_stage_points ps ON ps.rally_id = r.id
JOIN entries e ON e.id = ps.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
GROUP BY ce.championship_id, e.driver_id, pd.first_name, pd.last_name;
