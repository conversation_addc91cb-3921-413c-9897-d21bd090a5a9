-- Scalable Championship Database Structure
-- This design will handle unlimited championships and complex class rules

-- 1. Championship Classes Table - Maps which classes belong to which championships
CREATE TABLE championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    class_pattern VARCHAR(100) NOT NULL,  -- Pattern to match (e.g., 'C2', 'Rally3', '%Historic%')
    match_type VARCHAR(20) DEFAULT 'exact', -- 'exact', 'contains', 'starts_with', 'ends_with'
    priority INTEGER DEFAULT 1,           -- For handling overlapping rules
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    INDEX idx_championship_classes_championship (championship_id),
    INDEX idx_championship_classes_pattern (class_pattern)
);

-- 2. Championship Points System Table - Flexible points per championship
CREATE TABLE championship_points_system (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    position INTEGER NOT NULL,
    points INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(championship_id, position),
    INDEX idx_points_system_championship (championship_id)
);

-- 3. Championship Entry Eligibility Table - For special cases/overrides
CREATE TABLE championship_entry_eligibility (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    entry_id VARCHAR(255) NOT NULL,
    eligible BOOLEAN DEFAULT true,
    reason VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
    UNIQUE(championship_id, entry_id),
    INDEX idx_eligibility_championship (championship_id),
    INDEX idx_eligibility_entry (entry_id)
);

-- 4. Insert default points system (F1-style: 25, 18, 15, 12, 10, 8, 6, 4, 2, 1)
INSERT INTO championship_points_system (championship_id, position, points) VALUES
-- Default points for all championships
('default', 1, 25), ('default', 2, 18), ('default', 3, 15), ('default', 4, 12), ('default', 5, 10),
('default', 6, 8), ('default', 7, 6), ('default', 8, 4), ('default', 9, 2), ('default', 10, 1);

-- 5. Setup your current championships
-- Greece Championship - All C-classes
INSERT INTO championship_classes (championship_id, class_pattern, match_type) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C1', 'contains'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C2', 'contains'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C3', 'contains'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C4', 'contains'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C5', 'contains'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C6', 'contains');

-- Historic Championship - Numeric classes only (exact match)
INSERT INTO championship_classes (championship_id, class_pattern, match_type) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '2', 'exact'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '3', 'exact'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '4', 'exact');

-- Historic Gravel Cup - Same as Historic
INSERT INTO championship_classes (championship_id, class_pattern, match_type) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '2', 'exact'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '3', 'exact'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '4', 'exact');

-- Rally3 Championship - C2 cars that are Rally3
INSERT INTO championship_classes (championship_id, class_pattern, match_type) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb', 'Rally3', 'contains');

-- 6. Function to check if entry is eligible for championship
DELIMITER //
CREATE FUNCTION is_entry_eligible_for_championship(
    p_entry_id VARCHAR(255),
    p_championship_id VARCHAR(255)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_entry_class TEXT;
    DECLARE v_eligible BOOLEAN DEFAULT FALSE;
    DECLARE v_override_eligible BOOLEAN;
    
    -- Get entry class
    SELECT class INTO v_entry_class 
    FROM entries 
    WHERE id = p_entry_id;
    
    -- Check for explicit eligibility override
    SELECT eligible INTO v_override_eligible
    FROM championship_entry_eligibility
    WHERE championship_id = p_championship_id AND entry_id = p_entry_id;
    
    -- If override exists, use it
    IF v_override_eligible IS NOT NULL THEN
        RETURN v_override_eligible;
    END IF;
    
    -- Check class patterns
    SELECT COUNT(*) > 0 INTO v_eligible
    FROM championship_classes cc
    WHERE cc.championship_id = p_championship_id
    AND cc.active = true
    AND (
        (cc.match_type = 'exact' AND v_entry_class = cc.class_pattern) OR
        (cc.match_type = 'contains' AND v_entry_class LIKE CONCAT('%', cc.class_pattern, '%')) OR
        (cc.match_type = 'starts_with' AND v_entry_class LIKE CONCAT(cc.class_pattern, '%')) OR
        (cc.match_type = 'ends_with' AND v_entry_class LIKE CONCAT('%', cc.class_pattern))
    );
    
    RETURN v_eligible;
END //
DELIMITER ;

-- 7. Championship Results View (using the function)
CREATE VIEW championship_results AS
SELECT 
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    -- Get points from championship-specific or default points system
    COALESCE(cps.points, def_cps.points, 0) as points
    
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
LEFT JOIN championship_points_system cps ON ce.championship_id = cps.championship_id AND oc.position = cps.position
LEFT JOIN championship_points_system def_cps ON def_cps.championship_id = 'default' AND oc.position = def_cps.position

WHERE e.status IN ('finished', 'retired', 'dnf')
AND is_entry_eligible_for_championship(e.id, ce.championship_id) = true

ORDER BY ce.championship_id, r.start_date, oc.position;

-- 8. Championship Standings View
CREATE VIEW championship_standings AS
SELECT 
    championship_id,
    championship_name,
    driver,
    driver_nationality,
    SUM(points) as total_points,
    COUNT(*) as rallies_completed,
    ROW_NUMBER() OVER (PARTITION BY championship_id ORDER BY SUM(points) DESC) as position
FROM championship_results
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, total_points DESC;
