-- Recreate Championship Views
-- These were dropped by CASCADE and need to be recreated

-- 1. Recreate championship_standings view
CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- 2. Recreate championship_class_standings view
CREATE VIEW championship_class_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.class,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS class_points,
  RANK() OVER (
    PARTITION BY cp.championship_id, cp.class
    ORDER BY SUM(cp.rally_points) DESC
  ) AS class_position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    e.class,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id, e.class
) psp_total ON psp_total.championship_id = cp.championship_id
  AND psp_total.driver_id = cp.driver_id
  AND psp_total.class = cp.class
GROUP BY cp.championship_id, cp.championship_name, cp.class, cp.driver_id, cp.driver;

-- 3. Verify all views are created
SELECT 'Championship views status:' as status;
SELECT schemaname, viewname 
FROM pg_views 
WHERE viewname LIKE '%championship%' 
ORDER BY viewname;

-- 4. Test the fixed championship results
SELECT 'Fixed Championship Results for Rally Stereas Elladas 2025:' as test;
SELECT 
    championship_name,
    class,
    driver,
    championship_position,
    total_time
FROM championship_overall_classification
WHERE rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_name, championship_position;

-- 5. Test the fixed championship standings
SELECT 'Fixed Championship Standings (Top 3 per championship):' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points,
    total_power_stage_points,
    grand_total_points
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position;

-- 6. Show championship eligibility rules
SELECT 'Championship Eligibility Rules:' as rules;
SELECT 
    c.name as championship,
    ce.class_pattern,
    ce.description
FROM championship_eligibility ce
JOIN championships c ON c.id = ce.championship_id
ORDER BY c.name, ce.class_pattern;

-- 7. Show class distribution per championship
SELECT 'Class Distribution per Championship:' as distribution;
SELECT 
    coc.championship_name,
    coc.class,
    COUNT(*) as entry_count,
    COUNT(DISTINCT coc.driver) as unique_drivers
FROM championship_overall_classification coc
GROUP BY coc.championship_id, coc.championship_name, coc.class
ORDER BY coc.championship_name, entry_count DESC;

SELECT 'All championship views recreated and tested!' as result;
