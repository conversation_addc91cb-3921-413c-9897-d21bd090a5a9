-- Check Existing Data for Championship Migration
-- Run this to see what data you have and what needs to be linked

-- ============================================================================
-- STEP 1: Check existing championships
-- ============================================================================
SELECT 'EXISTING CHAMPIONSHIPS' as section;
SELECT 
  id,
  name,
  year,
  type,
  description
FROM championships 
ORDER BY year DESC, name;

-- ============================================================================
-- STEP 2: Check existing rallies
-- ============================================================================
SELECT 'EXISTING RALLIES' as section;
SELECT 
  id,
  name,
  country,
  start_date,
  status,
  (SELECT COUNT(*) FROM entries WHERE rally_id = r.id) as entry_count,
  (SELECT COUNT(*) FROM results WHERE rally_id = r.id) as result_count
FROM rallies r
ORDER BY start_date DESC;

-- ============================================================================
-- STEP 3: Check existing championship_events (rally-championship links)
-- ============================================================================
SELECT 'EXISTING CHAMPIONSHIP EVENTS' as section;
SELECT 
  ce.id,
  c.name as championship_name,
  c.year,
  r.name as rally_name,
  r.start_date,
  ce.coefficient,
  (SELECT COUNT(*) FROM entries WHERE rally_id = ce.rally_id) as entries,
  (SELECT COUNT(*) FROM results WHERE rally_id = ce.rally_id) as results
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
ORDER BY c.year DESC, r.start_date DESC;

-- ============================================================================
-- STEP 4: Check rallies NOT linked to any championship
-- ============================================================================
SELECT 'RALLIES NOT IN ANY CHAMPIONSHIP' as section;
SELECT 
  r.id,
  r.name,
  r.country,
  r.start_date,
  r.status,
  (SELECT COUNT(*) FROM entries WHERE rally_id = r.id) as entry_count,
  (SELECT COUNT(*) FROM results WHERE rally_id = r.id) as result_count
FROM rallies r
WHERE r.id NOT IN (SELECT rally_id FROM championship_events)
ORDER BY r.start_date DESC;

-- ============================================================================
-- STEP 5: Check if new views have data
-- ============================================================================
SELECT 'NEW CHAMPIONSHIP VIEWS DATA' as section;

SELECT 'championship_overall_classification' as view_name, COUNT(*) as row_count 
FROM championship_overall_classification
UNION ALL
SELECT 'championship_points' as view_name, COUNT(*) as row_count 
FROM championship_points
UNION ALL
SELECT 'championship_standings' as view_name, COUNT(*) as row_count 
FROM championship_standings
UNION ALL
SELECT 'championship_class_standings' as view_name, COUNT(*) as row_count 
FROM championship_class_standings;

-- ============================================================================
-- STEP 6: Sample championship data (if any)
-- ============================================================================
SELECT 'SAMPLE CHAMPIONSHIP DATA' as section;
SELECT 
  championship_name,
  COUNT(DISTINCT rally_id) as rallies,
  COUNT(DISTINCT driver_id) as drivers,
  AVG(coefficient) as avg_coefficient
FROM championship_points 
GROUP BY championship_id, championship_name
ORDER BY championship_name
LIMIT 5;

-- ============================================================================
-- STEP 7: Migration recommendations
-- ============================================================================
SELECT 'MIGRATION RECOMMENDATIONS' as section;

-- Count rallies that need championship assignment
WITH unlinked_rallies AS (
  SELECT COUNT(*) as count
  FROM rallies r
  WHERE r.id NOT IN (SELECT rally_id FROM championship_events)
    AND (SELECT COUNT(*) FROM entries WHERE rally_id = r.id) > 0
)
SELECT 
  CASE 
    WHEN count > 0 THEN 
      'You have ' || count || ' rallies with entries that are not linked to any championship. Consider linking them.'
    ELSE 
      'All rallies with entries are already linked to championships.'
  END as recommendation
FROM unlinked_rallies;
