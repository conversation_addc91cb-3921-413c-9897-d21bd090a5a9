-- Fix Championship Filtering
-- Add proper class-based filtering to championship views

-- 1. First, let's see the current problem
SELECT 'Current Problem - All entries in all championships:' as issue;
SELECT 
    championship_name,
    rally_name,
    class,
    driver,
    championship_position
FROM championship_overall_classification
WHERE rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_name, championship_position;

-- 2. Create a simple championship eligibility table
CREATE TABLE IF NOT EXISTS championship_eligibility (
    id SERIAL PRIMARY KEY,
    championship_id UUID NOT NULL,
    class_pattern VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE (championship_id, class_pattern)
);

-- 3. Define eligibility rules based on your championship structure
DELETE FROM championship_eligibility;

-- Greece Championship: F2, C-classes, N
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'F2', 'F2 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C1', 'C1 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C2', 'C2 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C3', 'C3 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C4', 'C4 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C5', 'C5 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C6', 'C6 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'N', 'N class');

-- Historic Championship: Numeric classes only
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '2', 'Historic class 2'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '3', 'Historic class 3'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '4', 'Historic class 4');

-- Historic Gravel Cup: Same as Historic
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '2', 'Historic Gravel class 2'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '3', 'Historic Gravel class 3'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '4', 'Historic Gravel class 4');

-- Rally3 Championship: RC1, RC2, specific Rally3 cars
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'RC1', 'RC1 class'),
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'RC2', 'RC2 class');

-- 4. Create a fixed championship_overall_classification view
DROP VIEW IF EXISTS championship_overall_classification CASCADE;

CREATE VIEW championship_overall_classification AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  e.class,
  e.car,
  SUM(res.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY ce.championship_id, e.rally_id
    ORDER BY SUM(res.time + COALESCE(p.time, 0))
  ) AS championship_position,
  SUM(res.time + COALESCE(p.time, 0)) - MIN(SUM(res.time + COALESCE(p.time, 0))) OVER (
    PARTITION BY ce.championship_id, e.rally_id
  ) AS time_diff,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN entries e ON e.rally_id = ce.rally_id
JOIN results res ON res.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
-- ADD CHAMPIONSHIP ELIGIBILITY FILTER
AND EXISTS (
    SELECT 1 FROM championship_eligibility elig 
    WHERE elig.championship_id = ce.championship_id 
    AND (
        e.class = elig.class_pattern OR 
        e.class LIKE '%' || elig.class_pattern || '%'
    )
)
GROUP BY ce.championship_id, c.name, e.rally_id, r.name, e.id, e.number,
         pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.class, e.car, ce.coefficient;

-- 5. Recreate dependent views
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

-- 6. Test the fix
SELECT 'Fixed Championship Results:' as test;
SELECT 
    championship_name,
    rally_name,
    class,
    driver,
    championship_position
FROM championship_overall_classification
WHERE rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_name, championship_position;

SELECT 'Fixed Championship Standings:' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position;

SELECT 'Championship filtering fixed!' as result;
