import fetch from 'node-fetch';

async function testHistoricChampionship() {
  try {
    console.log('=== Testing Historic Championship ===');
    
    const baseUrl = 'http://localhost:5174/api/championship-results';
    
    // Get Rally Stereas Elladas 2025 ID
    const ralliesResponse = await fetch('http://localhost:5174/api/rallies');
    const rallies = await ralliesResponse.json();
    const strereasRally = rallies.find(r => r.name.includes('Stereas Elladas 2025'));
    
    if (!strereasRally) {
      console.log('Rally not found');
      return;
    }
    
    // Get championships for this rally
    const rallyChampionshipsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/championships`);
    const rallyChampionships = await rallyChampionshipsResponse.json();
    
    const historicChampionship = rallyChampionships.find(c => c.championship_name === 'Historic');
    const historicGravelChampionship = rallyChampionships.find(c => c.championship_name === 'Historic Gravel Cup');
    const rally3Championship = rallyChampionships.find(c => c.championship_name === 'Rally3');
    
    if (historicChampionship) {
      console.log('\n--- Historic Championship Results ---');
      const resultsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/results?championshipId=${historicChampionship.championship_id}`);
      const results = await resultsResponse.json();
      console.log(`Found ${results.length} entries in Historic championship:`);
      results.forEach(result => {
        console.log(`  ${result.championship_position}. ${result.driver} (${result.points} pts) - ${result.class} - ${result.car}`);
      });
      
      console.log('\n--- Historic Championship Standings ---');
      const standingsResponse = await fetch(`${baseUrl}/championships/${historicChampionship.championship_id}/standings`);
      const standings = await standingsResponse.json();
      console.log(`Found ${standings.length} drivers in Historic championship standings:`);
      standings.forEach(driver => {
        console.log(`  ${driver.position}. ${driver.driver} (${driver.total_points} pts)`);
      });
    }
    
    if (historicGravelChampionship) {
      console.log('\n--- Historic Gravel Cup Results ---');
      const resultsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/results?championshipId=${historicGravelChampionship.championship_id}`);
      const results = await resultsResponse.json();
      console.log(`Found ${results.length} entries in Historic Gravel Cup:`);
      results.slice(0, 10).forEach(result => {
        console.log(`  ${result.championship_position}. ${result.driver} (${result.points} pts) - ${result.class}`);
      });
    }
    
    if (rally3Championship) {
      console.log('\n--- Rally3 Championship Results ---');
      const resultsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/results?championshipId=${rally3Championship.championship_id}`);
      const results = await resultsResponse.json();
      console.log(`Found ${results.length} entries in Rally3 championship:`);
      results.forEach(result => {
        console.log(`  ${result.championship_position}. ${result.driver} (${result.points} pts) - ${result.class} - ${result.car}`);
      });
    }
    
    console.log('\n✅ Historic championship testing complete!');
    process.exit(0);
  } catch (error) {
    console.error('Error testing historic championship:', error);
    process.exit(1);
  }
}

testHistoricChampionship();
