-- Simpler Championship Structure - Easy to understand and maintain

-- 1. Championship Classes - Simple mapping table
CREATE TABLE championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id VARCHAR(255) NOT NULL,
    class_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE(championship_id, class_name),
    INDEX idx_championship_classes (championship_id, class_name)
);

-- 2. Setup your current championships
-- Greece Championship - All C-classes
INSERT INTO championship_classes (championship_id, class_name) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C1'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C2'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C3'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C4'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C5'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3', 'C6');

-- Historic Championship - Numeric classes
INSERT INTO championship_classes (championship_id, class_name) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '2'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '3'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17', '4');

-- Historic Gravel Cup - Same as Historic
INSERT INTO championship_classes (championship_id, class_name) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '2'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '3'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c', '4');

-- Rally3 Championship - Rally3 class
INSERT INTO championship_classes (championship_id, class_name) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb', 'Rally3');

-- 3. Simple Championship Results View
CREATE VIEW championship_results AS
SELECT 
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    -- Standard F1 points
    CASE 
        WHEN oc.position = 1 THEN 25
        WHEN oc.position = 2 THEN 18
        WHEN oc.position = 3 THEN 15
        WHEN oc.position = 4 THEN 12
        WHEN oc.position = 5 THEN 10
        WHEN oc.position = 6 THEN 8
        WHEN oc.position = 7 THEN 6
        WHEN oc.position = 8 THEN 4
        WHEN oc.position = 9 THEN 2
        WHEN oc.position = 10 THEN 1
        ELSE 0
    END as points
    
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id

WHERE e.status IN ('finished', 'retired', 'dnf')
AND EXISTS (
    SELECT 1 FROM championship_classes cc 
    WHERE cc.championship_id = ce.championship_id 
    AND (
        -- Exact match
        e.class = cc.class_name 
        -- Contains match (for comma-separated classes)
        OR e.class LIKE CONCAT('%', cc.class_name, '%')
    )
)

ORDER BY ce.championship_id, r.start_date, oc.position;

-- 4. Championship Standings View
CREATE VIEW championship_standings AS
SELECT 
    championship_id,
    championship_name,
    driver,
    driver_nationality,
    SUM(points) as total_points,
    COUNT(*) as rallies_completed,
    ROW_NUMBER() OVER (PARTITION BY championship_id ORDER BY SUM(points) DESC) as position
FROM championship_results
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, total_points DESC;

-- 5. Easy management functions

-- Add a new championship with classes
-- Example: Add WRC Championship
/*
INSERT INTO championships (id, name, year) VALUES ('wrc-2025', 'WRC Championship', 2025);
INSERT INTO championship_classes (championship_id, class_name) VALUES
('wrc-2025', 'WRC'),
('wrc-2025', 'Rally1');
*/

-- Add rally to championship
/*
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('wrc-2025', 'rally-id', 1.0);
*/

-- View championship setup
SELECT 
    c.name as championship,
    STRING_AGG(cc.class_name, ', ' ORDER BY cc.class_name) as classes
FROM championships c
LEFT JOIN championship_classes cc ON c.id = cc.championship_id
GROUP BY c.id, c.name
ORDER BY c.name;

-- Test the system
SELECT 'Championship Results Test:' as test;
SELECT 
    championship_name,
    COUNT(*) as total_entries,
    COUNT(DISTINCT driver) as unique_drivers,
    STRING_AGG(DISTINCT class, ', ' ORDER BY class) as classes_found
FROM championship_results
WHERE rally_name LIKE '%Stereas Elladas%'
GROUP BY championship_id, championship_name
ORDER BY total_entries DESC;
