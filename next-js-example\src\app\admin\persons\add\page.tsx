'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';

export default function AddPersonPage() {
  const [form, setForm] = useState({
    first_name: '',
    last_name: '',
    nationality: '',
    date_of_birth: '',
    photo_url: '',
    bio: ''
  });
  const [flagCode, setFlagCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Update flag code when nationality changes
  useEffect(() => {
    setFlagCode(getCountryCode(form.nationality));
  }, [form.nationality]);

  // Helper function to get country code from nationality
  const getCountryCode = (nationality: string): string => {
    // This is a placeholder - you would implement your actual country code logic here
    return nationality.toLowerCase().substring(0, 2);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const { error } = await supabase
        .from('persons')
        .insert([form]);
      
      if (error) throw error;
      
      router.push('/admin/persons');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-lg mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Add Person</h1>
        {error && <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">{error}</div>}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <input
              name="first_name"
              value={form.first_name}
              onChange={handleChange}
              required
              placeholder="First Name"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
            <input
              name="last_name"
              value={form.last_name}
              onChange={handleChange}
              required
              placeholder="Last Name"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="relative">
            <input
              name="nationality"
              value={form.nationality}
              onChange={handleChange}
              required
              placeholder="Nationality"
              className={`w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white ${flagCode ? 'pl-10' : ''}`}
            />
            {flagCode && (
              <span
                className={`fi fi-${flagCode} absolute left-3 top-1/2 transform -translate-y-1/2`}
                style={{ width: '20px', height: '15px' }}
              ></span>
            )}
          </div>
          <div className="grid grid-cols-1 gap-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Date of Birth
              <input
                name="date_of_birth"
                value={form.date_of_birth}
                onChange={handleChange}
                type="date"
                className="mt-1 w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
              />
            </label>
          </div>
          <input
            name="photo_url"
            value={form.photo_url}
            onChange={handleChange}
            placeholder="Photo URL"
            className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
          />
          <textarea
            name="bio"
            value={form.bio}
            onChange={handleChange}
            placeholder="Biography"
            rows={4}
            className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
          />
          <div className="flex justify-between">
            <button
              type="button"
              onClick={() => router.push('/admin/persons')}
              className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Adding...' : 'Add Person'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
