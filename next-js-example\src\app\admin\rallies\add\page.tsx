import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import RallyForm from "@/components/admin/rally-form";

export default async function NewRallyPage() {
  const supabase = await createClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Fetch championships for dropdown
  const { data: championships } = await supabase
    .from("championships")
    .select("id, name, year");

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">Add New Rally</h1>
      <RallyForm championships={championships || []} />
    </div>
  );
}
