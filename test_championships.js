#!/usr/bin/env node

// Simple test script to check championship functionality
// Run this with: node test_championships.js

import pool from './server/config/db.js';

async function testChampionships() {
  try {
    console.log('=== CHAMPIONSHIP DEBUG TEST ===\n');
    
    // 1. List all championships in database
    console.log('1. All championships in database:');
    const allChampionships = await pool.query('SELECT id, name, year, type FROM championships ORDER BY name');
    
    if (allChampionships.rows.length === 0) {
      console.log('   ❌ NO CHAMPIONSHIPS FOUND IN DATABASE!');
      console.log('   This is why the import is failing.');
      console.log('   You need to create championships first.');
    } else {
      allChampionships.rows.forEach((champ, index) => {
        console.log(`   ${index + 1}. "${champ.name}" (${champ.year}) [${champ.type}] ID: ${champ.id}`);
      });
    }
    
    console.log(`\n   Total: ${allChampionships.rows.length} championships found\n`);
    
    // 2. Test the specific championships from JSON
    const championshipsFromJSON = [
      "Greece",
      "Historic (GR)",
      "Rally3 (GR)",
      "Historic Gravel Cup (GR)"
    ];
    
    console.log('2. Testing championships from JSON:');
    
    for (const championshipName of championshipsFromJSON) {
      try {
        console.log(`\n   Testing: "${championshipName}"`);
        
        // Try exact match first
        const exactMatch = await pool.query(
          'SELECT id, name, year, type FROM championships WHERE name = $1',
          [championshipName]
        );
        
        // Try case-insensitive match
        const caseInsensitiveMatch = await pool.query(
          'SELECT id, name, year, type FROM championships WHERE LOWER(name) = LOWER($1)',
          [championshipName.trim()]
        );
        
        console.log(`   - Exact match: ${exactMatch.rows.length} results`);
        console.log(`   - Case-insensitive match: ${caseInsensitiveMatch.rows.length} results`);
        
        if (caseInsensitiveMatch.rows.length > 0) {
          console.log(`   ✅ FOUND: "${caseInsensitiveMatch.rows[0].name}" (ID: ${caseInsensitiveMatch.rows[0].id})`);
        } else {
          console.log(`   ❌ NOT FOUND: "${championshipName}"`);
          
          // Try to find similar names
          const similarNames = await pool.query(
            'SELECT name FROM championships WHERE name ILIKE $1',
            [`%${championshipName.split(' ')[0]}%`]
          );
          
          if (similarNames.rows.length > 0) {
            console.log(`   💡 Similar names found:`);
            similarNames.rows.forEach(row => {
              console.log(`      - "${row.name}"`);
            });
          }
        }
      } catch (error) {
        console.log(`   ❌ ERROR testing "${championshipName}": ${error.message}`);
      }
    }
    
    // 3. Summary
    console.log('\n=== SUMMARY ===');
    
    if (allChampionships.rows.length === 0) {
      console.log('❌ No championships exist in database');
      console.log('   Solution: Create championships first');
    } else {
      const foundCount = championshipsFromJSON.filter(async name => {
        const result = await pool.query(
          'SELECT id FROM championships WHERE LOWER(name) = LOWER($1)',
          [name.trim()]
        );
        return result.rows.length > 0;
      }).length;
      
      console.log(`📊 Championships in JSON: ${championshipsFromJSON.length}`);
      console.log(`📊 Championships in DB: ${allChampionships.rows.length}`);
      console.log(`📊 Expected matches: Need to check individually above`);
    }
    
    console.log('\n✅ Championship test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await pool.end();
  }
}

// Run the test
testChampionships();
